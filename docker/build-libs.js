/* eslint-disable @typescript-eslint/no-var-requires */
const fs = require('fs');
const { Command, Option } = require('commander');
const child_process = require('child_process');
const util = require('util');
const path = require('path');

const exec = util.promisify(child_process.exec);

const program = new Command();

program.addOption(new Option('--libs <libs...>', 'specify paths to shared libs').makeOptionMandatory()).parse();

const options = program.opts();

const libsPaths = options.libs.map((libPath) => path.resolve(__dirname, libPath));

const libsProdPath = './dist/index.js';

function changeEntrypoint(packagePath, entryPath) {
  const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
  packageJson.main = entryPath;
  fs.writeFileSync(packagePath, JSON.stringify(packageJson, null, 2));
}

(async function () {
  libsPaths.forEach((path) => changeEntrypoint(`${path}/package.json`, libsProdPath));

  await Promise.all(
    libsPaths.map(async (path) => {
      const { stdout } = await exec(`npm run --prefix ${path} build`);
      console.log(stdout);
    }),
  );
})();
