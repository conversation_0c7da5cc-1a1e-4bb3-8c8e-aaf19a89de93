version: 0.2

phases:
  pre_build:
    commands:
      - echo Logging in Amazon ECR...
      - aws ecr get-login-password --region $AWS_DEFAULT_REGION | docker login --username AWS --password-stdin $AWS_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com
  build:
    commands:
      - echo Build started on `date`
      - echo Building the Docker image...
      - docker build -f $DOCKERFILE -t $IMAGE .
      - COMMIT_HASH=$(echo $CODEBUILD_RESOLVED_SOURCE_VERSION | cut -c 1-7)
      - IMAGE_TAG=${COMMIT_HASH:=latest}
      - IMAGE_URI=$ECR_REPO_URI:$IMAGE_TAG
      - docker tag $IMAGE:latest $IMAGE_URI
  post_build:
    commands:
      - echo Build completed.
      - echo Pushing the Docker image...
      - echo $IMAGE_URI
      - docker push $IMAGE_URI
      - echo Writing image definitions file...
      - aws lambda update-function-code --function-name $FUNCTION --image-uri $IMAGE_URI > /dev/null
