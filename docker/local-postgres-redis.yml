version: '3'

services:
  db:
    image: postgres:16-alpine
    container_name: compass_db
    restart: on-failure
    env_file:
      - '../services/compass/.env'
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - '5434:5432'

  redis:
    image: redis:7-alpine
    container_name: compass_redis
    command: redis-server --appendonly yes --requirepass password123456
    volumes:
      - redis_data:/data
    ports:
      - '6380:6379'

volumes:
  postgres_data:
  redis_data:
