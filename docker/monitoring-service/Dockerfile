# Build stage
FROM --platform=linux/amd64 public.ecr.aws/lambda/nodejs:20 AS builder

WORKDIR /handler

COPY tsconfig.json ./
COPY docker/build-libs.js ./
COPY package.json package-lock.json ./
COPY libs/common ./libs/common
COPY libs/database ./libs/database
COPY libs/monitoring ./libs/monitoring
COPY services/monitoring ./services/monitoring

RUN npm ci

RUN npm run --prefix=./libs/database prisma:generate

RUN node build-libs.js --libs ./libs/database ./libs/common ./libs/monitoring

RUN npm run --prefix=./services/monitoring build

# Production dependencies stage
FROM builder AS prod_dependencies

WORKDIR /handler

COPY package.json package-lock.json ./
COPY --from=builder /handler/libs ./libs
COPY --from=builder /handler/services/monitoring ./services/monitoring

RUN npm ci --omit=dev

RUN npm run --prefix=./libs/database prisma:generate

# Run stage
FROM prod_dependencies AS runner

WORKDIR ${LAMBDA_TASK_ROOT}

COPY --from=prod_dependencies /handler/libs ./libs
COPY --from=prod_dependencies /handler/node_modules ./node_modules
COPY --from=prod_dependencies /handler/services/monitoring/dist/ ./

CMD ["index.handler"]