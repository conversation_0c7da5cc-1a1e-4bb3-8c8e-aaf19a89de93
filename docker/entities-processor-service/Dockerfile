# Build stage
FROM public.ecr.aws/docker/library/node:20.10.0-alpine AS builder

WORKDIR /server

COPY tsconfig.json ./
COPY docker/build-libs.js ./
COPY package.json package-lock.json ./
COPY libs/ ./libs
COPY services/entities-processor ./services/entities-processor

RUN npm ci

RUN npm run --prefix=./libs/database prisma:generate

RUN node build-libs.js --libs ./libs/database ./libs/common ./libs/invoice-bill ./libs/monitoring ./libs/widget

RUN npm --prefix=./services/entities-processor ci

RUN npm run --prefix=./services/entities-processor build

# Production dependencies stage
FROM builder AS prod_dependencies

WORKDIR /server

COPY package.json package-lock.json ./
COPY --from=builder /server/libs ./libs
COPY --from=builder /server/services/entities-processor ./services/entities-processor

RUN npm ci --omit=dev

RUN npm --prefix=./services/entities-processor ci --omit=dev

RUN npm run --prefix=./libs/database prisma:generate

# Run stage
FROM prod_dependencies AS runner

WORKDIR /server

COPY --from=prod_dependencies /server/libs ./libs
COPY --from=prod_dependencies /server/libs/common/our.saltedge.private.pem ./libs/common/dist/our.saltedge.private.pem
COPY --from=prod_dependencies /server/node_modules ./node_modules
COPY --from=prod_dependencies /server/services/entities-processor/dist/ ./services/entities-processor/
COPY --from=prod_dependencies /server/services/entities-processor/package.json /server/services/entities-processor/package-lock.json ./

CMD ["npm", "run", "start:prod:docker"]
