# Build stage
FROM --platform=linux/arm64 public.ecr.aws/docker/library/node:20.10.0-alpine AS builder

WORKDIR /server

COPY tsconfig.json ./
COPY docker/build-libs.js ./
COPY package.json package-lock.json ./
COPY libs/ ./libs
COPY services/compass ./services/compass

RUN npm ci

RUN npm run --prefix=./libs/database prisma:generate

RUN node build-libs.js --libs ./libs/database ./libs/common ./libs/invoice-bill ./libs/monitoring ./libs/widget

RUN npm --prefix=./services/compass ci

RUN npm run --prefix=./services/compass build

# Production dependencies stage
FROM builder AS prod_dependencies

WORKDIR /server

COPY package.json package-lock.json ./
COPY --from=builder /server/libs ./libs
COPY --from=builder /server/services/compass ./services/compass

RUN npm ci --omit=dev

RUN npm --prefix=./services/compass ci --omit=dev

RUN npm run --prefix=./libs/database prisma:generate

# Run stage
FROM prod_dependencies AS runner

WORKDIR /server

COPY --from=prod_dependencies /server/libs ./libs
COPY --from=prod_dependencies /server/libs/common/our.saltedge.private.pem ./libs/common/dist/our.saltedge.private.pem
COPY --from=prod_dependencies /server/node_modules ./node_modules
COPY --from=prod_dependencies /server/services/compass/node_modules ./services/compass/node_modules
COPY --from=prod_dependencies /server/services/compass/dist/ ./services/compass/
COPY --from=prod_dependencies /server/services/compass/package.json /server/services/compass/package-lock.json ./
COPY --from=prod_dependencies /server/services/compass/their.saltedge.public.pem ./

EXPOSE 8000

ENV PORT 8000
ENV HOSTNAME "0.0.0.0"

CMD ["npm", "run", "start:prod:docker"]
