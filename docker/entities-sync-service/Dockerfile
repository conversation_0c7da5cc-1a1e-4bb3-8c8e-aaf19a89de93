# Build stage
FROM --platform=linux/amd64 public.ecr.aws/lambda/nodejs:20 AS builder

WORKDIR /handler

COPY tsconfig.json ./
COPY docker/build-libs.js ./
COPY package.json package-lock.json ./
COPY libs/common ./libs/common
COPY libs/invoice-bill ./libs/invoice-bill
COPY libs/database ./libs/database
COPY services/entities-sync ./services/entities-sync

RUN npm ci

RUN npm run --prefix=./libs/database prisma:generate

RUN node build-libs.js --libs ./libs/common ./libs/invoice-bill ./libs/database

RUN npm run --prefix=./services/entities-sync build

# Production dependencies stage
FROM builder AS prod_dependencies

WORKDIR /handler

COPY package.json package-lock.json ./
COPY --from=builder /handler/libs ./libs
COPY --from=builder /handler/services/entities-sync ./services/entities-sync

RUN npm ci --omit=dev

RUN npm run --prefix=./libs/database prisma:generate

# Run stage
FROM prod_dependencies AS runner

WORKDIR ${LAMBDA_TASK_ROOT}

COPY --from=prod_dependencies /handler/libs ./libs
COPY --from=prod_dependencies /handler/libs/common/our.saltedge.private.pem ./libs/common/dist/our.saltedge.private.pem
COPY --from=prod_dependencies /handler/node_modules ./node_modules
COPY --from=prod_dependencies /handler/services/entities-sync/dist/ ./

CMD ["index.handler"]
