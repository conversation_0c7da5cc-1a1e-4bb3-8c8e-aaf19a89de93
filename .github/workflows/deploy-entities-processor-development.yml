name: Deploy entities-processor-service to development environment

on:
  push:
    branches:
      - develop
    paths:
      - 'services/entities-processor/**'
      - 'libs/**'
      - 'docker/build-libs.js'
      - 'docker/entities-processor-service/**'
      - 'package.json'
      - 'package-lock.json'
jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [20.x]
    steps:
      - uses: actions/checkout@v2
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}
      - name: Install global dependencies
        run: |
          npm install
      - name: Install entities-processor service dependencies and typecheck
        working-directory: ./services/entities-processor
        run: |
          npm install
          npm run typecheck
  deploy:
    name: Deploy
    runs-on: ubuntu-latest
    environment: development

    steps:
      - uses: chrnorm/deployment-action@v2
        name: Create GitHub deployment
        id: deployment
        with:
          token: '${{ github.token }}'
          environment: development
      - name: Checkout
        uses: actions/checkout@v3

      - name: Deployment on development environment STARTED
        if: always()
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_LINK_NAMES: true
          SLACK_COLOR: ${{ job.status }}
          SLACK_ICON: https://github.com/rtCamp.png?size=48
          SLACK_MESSAGE: 'STARTED'
          SLACK_TITLE: compass entities-processor development
          SLACK_USERNAME: Github Actions
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK_URL}}

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@13d241b293754004c80624b5567555c4a39ffbe3
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@aaf69d68aa3fb14c1d5a6be9ac61fe15b48453a2

      - name: Build, tag, and push image to Amazon ECR
        id: build-image
        env:
          ECR_REGISTRY: ${{ secrets.ECR_REGISTRY }}
          ECR_REPOSITORY: ${{ secrets.ECR_REPOSITORY_ENTITIES_PROCESSOR }}
          OUR_SALT_EDGE_PRIVATE_PEM: ${{ secrets.OUR_SALT_EDGE_PRIVATE_PEM }}
        run: |
          # Download saltedge key
          aws ssm get-parameter --name $OUR_SALT_EDGE_PRIVATE_PEM --with-decryption | jq -r .Parameter.Value > libs/common/our.saltedge.private.pem

          # Build a docker container and push it to ECR 
          docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:develop -f docker/entities-processor-service/Dockerfile .
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:develop
          echo "::set-output name=image::$ECR_REGISTRY/$ECR_REPOSITORY:develop"

      - name: Pull image and restart docker container
        uses: appleboy/ssh-action@v0.1.2
        env:
          IMAGE: ${{ steps.build-image.outputs.image }}
        with:
          host: ${{secrets.SSH_DEV_HOST}}
          username: ${{ secrets.SSH_DEV_USERNAME }}
          key: ${{ secrets.SSH_DEV_PRIVATE_KEY  }}
          envs: IMAGE
          script: |
            aws ecr get-login-password --region eu-central-1 --profile compass | docker login --username AWS --password-stdin 303670496204.dkr.ecr.eu-central-1.amazonaws.com
            docker pull $IMAGE
            cd /srv/compass/entities-processor-service 
            docker-compose up -d

      - name: Send success
        if: success()
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_LINK_NAMES: true
          SLACK_COLOR: ${{ job.status }}
          SLACK_ICON: https://github.com/rtCamp.png?size=48
          SLACK_MESSAGE: 'SUCCEEDED'
          SLACK_TITLE: compass entities-processor development
          SLACK_USERNAME: Github Actions
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK_URL}}

      - name: Send failure
        if: failure()
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_LINK_NAMES: true
          SLACK_COLOR: ${{ job.status }}
          SLACK_ICON: https://github.com/rtCamp.png?size=48
          SLACK_MESSAGE: '@channel'
          SLACK_TITLE: compass entities-processor development FAILED
          SLACK_USERNAME: Github Actions
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK_URL}}
