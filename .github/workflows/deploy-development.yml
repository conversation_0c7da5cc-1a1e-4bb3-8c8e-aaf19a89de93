name: Deployment on development environment
on:
  push:
    branches:
      - develop
    paths:
      - 'services/compass/**'
      - 'libs/**'
      - 'docker/build-libs.js'
      - 'docker/compass-service/**'
      - 'package.json'
      - 'package-lock.json'

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [20.x]
    steps:
      - uses: actions/checkout@v2
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}
      - name: Install global dependencies
        run: |
          npm install
      - name: Install compass service dependencies and typecheck
        working-directory: ./services/compass
        run: |
          npm install
          npm run typecheck

  deploy:
    needs: [test]
    runs-on: ubuntu-latest
    permissions:
      deployments: write
    steps:
      - uses: chrnorm/deployment-action@v2
        name: Create GitHub deployment
        id: deployment
        with:
          token: '${{ github.token }}'
          environment-url: https://dev-api.compassapp.ai
          environment: development
      - name: Deployment on development environment STARTED
        if: always()
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_LINK_NAMES: true
          SLACK_COLOR: ${{ job.status }}
          SLACK_ICON: https://github.com/rtCamp.png?size=48
          SLACK_MESSAGE: 'STARTED'
          SLACK_TITLE: compass service development
          SLACK_USERNAME: Github Actions
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK_URL}}
      - name: Deploy Compass Development Api
        uses: appleboy/ssh-action@v0.1.2
        with:
          host: ${{ secrets.SSH_DEV_HOST }}
          username: ${{ secrets.SSH_DEV_USERNAME }}
          key: ${{ secrets.SSH_DEV_PRIVATE_KEY }}
          script: |
            cd ~/compass-api/
            git reset --hard HEAD && git clean -fd
            git checkout develop
            git pull -r
            export NVM_DIR=~/.nvm
            source ~/.nvm/nvm.sh
            node docker/build-libs.js --libs ../libs/common ../libs/database ../libs/monitoring ../libs/widget ../libs/invoice-bill
            npm install
            cd ~/compass-api/libs/database
            npm run migrate:prod || { echo 'Migration failed'; exit 1; }
            npm run prisma:generate || { echo 'Prisma generation failed'; exit 1; }
            cd ~/compass-api/services/compass
            npm install
            npm run build || { echo 'Build failed'; exit 1; }
            NODE_ENV=production pm2 restart server --log-date-format="YYYY-MM-DD HH:mm Z"
      - name: Update deployment status (success)
        if: success()
        uses: chrnorm/deployment-status@v2
        with:
          token: '${{ github.token }}'
          environment-url: ${{ steps.deployment.outputs.environment_url }}
          deployment-id: ${{ steps.deployment.outputs.deployment_id }}
          state: 'success'

      - name: Update deployment status (failure)
        if: failure()
        uses: chrnorm/deployment-status@v2
        with:
          token: '${{ github.token }}'
          environment-url: ${{ steps.deployment.outputs.environment_url }}
          deployment-id: ${{ steps.deployment.outputs.deployment_id }}
          state: 'failure'

      - name: Send success
        if: success()
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_LINK_NAMES: true
          SLACK_COLOR: ${{ job.status }}
          SLACK_ICON: https://github.com/rtCamp.png?size=48
          SLACK_MESSAGE: 'SUCCEEDED'
          SLACK_TITLE: compass service development
          SLACK_USERNAME: Github Actions
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK_URL}}

      - name: Send failure
        if: failure()
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_LINK_NAMES: true
          SLACK_COLOR: ${{ job.status }}
          SLACK_ICON: https://github.com/rtCamp.png?size=48
          SLACK_MESSAGE: '@channel'
          SLACK_TITLE: compass service development FAILED
          SLACK_USERNAME: Github Actions
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK_URL}}
