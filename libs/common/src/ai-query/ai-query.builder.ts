import { Prisma } from '@prisma/client';

import constants from './ai-query.constants';
import type { AiDisplayQueryType } from './ai-query.types';

const transactionAiDisplayQueryValidatorSql = {
  filterColumnsSql: Prisma.raw(`
    id,
    "currency",
    "amount",
    "convertedAmount",
    "paymentMethod",
    type,
    date,
    description,
    "accountNumber",
    "accountName",
    "bankAccountId",
    "privateMerchantId",
    "privateMerchantName",
    "leafCategoryId",
    "leafCategoryName",
    "createdAt",
    "updatedAt"
  `),
  fallbackSql: Prisma.raw(`
    0::integer AS id,
    'EUR'::"Currency" AS "currency",
    0::float AS "amount",
    '{}'::jsonb AS "convertedAmount",
    'BANK'::"PaymentMethod" AS "paymentMethod",
    'EXPENSE'::"FinancialType" AS type,
    '2024-01-01'::timestamp AS date,
    'test'::citext AS description,
    'test'::citext AS "accountNumber",
    'test'::citext AS "accountName",
    0::integer AS "bankAccountId",
    0::integer AS "privateMerchantId",
    'test'::citext AS "privateMerchantName",
    0::integer AS "leafCategoryId",
    'test'::text AS "leafCategoryName",
    '2024-01-01'::timestamp AS "createdAt",
    '2024-01-01'::timestamp AS "updatedAt"
  `),
};

const balanceAiDisplayQueryValidatorSql = {
  filterColumnsSql: Prisma.raw(`
    id,
    "displayName",
    "number",
    "currency",
    "currentBalance",
    "currentConvertedBalance",
    "date",
    "createdAt",
    "updatedAt"
  `),
  fallbackSql: Prisma.raw(`
    0::integer AS id,
    'test'::citext AS "displayName",
    'test'::text AS "number",
    'EUR'::"Currency" AS "currency",
    0::float AS "currentBalance",
    '{}'::jsonb AS "currentConvertedBalance",
    '2024-01-01'::date AS "date",
    '2024-01-01'::timestamp AS "createdAt",
    '2024-01-01'::timestamp AS "updatedAt"
  `),
};

const dailyBalanceAiDisplayQueryValidatorSql = {
  filterColumnsSql: Prisma.raw(`
    id,
    "balance",
    "convertedBalance",
    "date",
    "bankAccountId",
    "displayName",
    "number",
    "currency",
    "createdAt",
    "updatedAt"
  `),
  fallbackSql: Prisma.raw(`
    0::integer AS id,
    0::float AS "balance",
    '{}'::jsonb AS "convertedBalance",
    '2024-01-01'::date AS "date",
    0::integer AS "bankAccountId",
    'test'::citext AS "displayName",
    'test'::text AS "number",
    'EUR'::"Currency" AS "currency",
    '2024-01-01'::timestamp AS "createdAt",
    '2024-01-01'::timestamp AS "updatedAt"
  `),
};

const invoiceAiDisplayQueryValidatorSql = {
  filterColumnsSql: Prisma.raw(`
    id,
    "amount",
    "currency",
    "convertedTotalAmount",
    "lineDescriptions",
    "note",
    "isPaid",
    "issueDate",
    "dueDate",
    "docNumber",
    "privateMerchantId",
    "privateMerchantName",
    "leafCategoryId",
    "leafCategoryName",
    "createdAt",
    "updatedAt"
  `),
  fallbackSql: Prisma.raw(`
    0::integer AS id,
    0::float AS "amount",
    'EUR'::"Currency" AS "currency",
    '{}'::jsonb AS "convertedTotalAmount",
    'test'::text AS "lineDescriptions",
    'test'::text AS "note",
    TRUE::boolean AS "isPaid",
    '2024-01-01'::timestamp AS "issueDate",
    '2024-01-01'::timestamp AS "dueDate",
    'test'::text AS "docNumber",
    0::integer AS "privateMerchantId",
    'test'::citext AS "privateMerchantName",
    0::integer AS "leafCategoryId",
    'test'::text AS "leafCategoryName",
    '2024-01-01'::timestamp AS "createdAt",
    '2024-01-01'::timestamp AS "updatedAt"
  `),
};

const billAiDisplayQueryValidatorSql = {
  filterColumnsSql: Prisma.raw(`
    id,
    "amount",
    "currency",
    "convertedTotalAmount",
    "lineDescriptions",
    "note",
    "isPaid",
    "issueDate",
    "dueDate",
    "docNumber",
    "privateMerchantId",
    "privateMerchantName",
    "leafCategoryId",
    "leafCategoryName",
    "createdAt",
    "updatedAt"
  `),
  fallbackSql: Prisma.raw(`
    0::integer AS id,
    0::float AS "amount",
    'EUR'::"Currency" AS "currency",
    '{}'::jsonb AS "convertedTotalAmount",
    'test'::text AS "lineDescriptions",
    'test'::text AS "note",
    TRUE::boolean AS "isPaid",
    '2024-01-01'::timestamp AS "issueDate",
    '2024-01-01'::timestamp AS "dueDate",
    'test'::text AS "docNumber",
    0::integer AS "privateMerchantId",
    'test'::citext AS "privateMerchantName",
    0::integer AS "leafCategoryId",
    'test'::text AS "leafCategoryName",
    '2024-01-01'::timestamp AS "createdAt",
    '2024-01-01'::timestamp AS "updatedAt"
  `),
};

function buildAiDisplayQueryValidatorSql(aiDisplayQuery: string, aiDisplayQueryType: AiDisplayQueryType) {
  let validatorSql: { filterColumnsSql: Prisma.Sql; fallbackSql: Prisma.Sql } | null = null;

  switch (aiDisplayQueryType) {
    case constants.aiDisplayQueryType.transaction:
      validatorSql = transactionAiDisplayQueryValidatorSql;
      break;

    case constants.aiDisplayQueryType.balance:
      validatorSql = balanceAiDisplayQueryValidatorSql;
      break;

    case constants.aiDisplayQueryType.dailyBalance:
      validatorSql = dailyBalanceAiDisplayQueryValidatorSql;
      break;

    case constants.aiDisplayQueryType.invoice:
      validatorSql = invoiceAiDisplayQueryValidatorSql;
      break;

    case constants.aiDisplayQueryType.bill:
      validatorSql = billAiDisplayQueryValidatorSql;
      break;
  }

  if (!validatorSql) {
    return { type: constants.aiDisplayQueryValidatorType.syntax, sql: Prisma.raw(`EXPLAIN ${aiDisplayQuery}`) };
  }

  return {
    type: constants.aiDisplayQueryValidatorType.data,
    sql: Prisma.raw(`
      WITH "aiAssistantQuery" AS (${aiDisplayQuery}),

      "filteredColumns" AS (
        SELECT ${validatorSql.filterColumnsSql.text} FROM "aiAssistantQuery" LIMIT 1
      )

      SELECT * FROM "filteredColumns"

      UNION ALL

      SELECT ${validatorSql.fallbackSql.text} WHERE NOT EXISTS (SELECT 1 FROM "filteredColumns");
  `),
  };
}

export default { buildAiDisplayQueryValidatorSql };
