import {
  BankAccount,
  BankAccountDailyBalance,
  Bill,
  Invoice,
  LeafCategory,
  Prisma,
  PrivateMerchant,
  Transaction,
} from '@prisma/client';

import constants from './ai-query.constants';

export type AiDisplayQueryType = keyof typeof constants.aiDisplayQueryType;

export type ExecuteAiQueryAndReturnNumberOfRowsArgs = {
  sql: string;
  companyId: number;
  validate: boolean;
};
export type ExecuteAiQueryAndReturnItemsArgs = { sql: string; companyId: number };

export type ExecuteAiDisplayQueryArgs = { sql: string; companyId: number };
export type ValidateAiDisplayQueryArgs = {
  sql: Prisma.Sql;
  companyId: number;
  type: keyof typeof constants.aiDisplayQueryValidatorType;
};

type OptionalPrivateMerchantAndLeafCategoryName = {
  privateMerchantName: PrivateMerchant['displayName'] | null;
  leafCategoryName: LeafCategory['name'] | null;
};

export type TransactionAiDisplayQueryItem = Pick<
  Transaction,
  | 'id'
  | 'currency'
  | 'amount'
  | 'convertedAmount'
  | 'paymentMethod'
  | 'type'
  | 'date'
  | 'description'
  | 'accountNumber'
  | 'accountName'
  | 'bankAccountId'
  | 'privateMerchantId'
  | 'leafCategoryId'
  | 'createdAt'
  | 'updatedAt'
> &
  OptionalPrivateMerchantAndLeafCategoryName;

export type BalanceAiDisplayQueryItem = Pick<
  BankAccount,
  | 'id'
  | 'displayName'
  | 'number'
  | 'currency'
  | 'currentBalance'
  | 'currentConvertedBalance'
  | 'createdAt'
  | 'updatedAt'
> & { date: BankAccountDailyBalance['date'] | null };

export type DailyBalanceAiDisplayQueryItem = Pick<
  BankAccountDailyBalance,
  'id' | 'balance' | 'convertedBalance' | 'date' | 'bankAccountId' | 'createdAt' | 'updatedAt'
> & { displayName: BankAccount['displayName']; number: BankAccount['number']; currency: BankAccount['currency'] };

export type InvoiceAiDisplayQueryItem = Pick<
  Invoice,
  | 'id'
  | 'amount'
  | 'currency'
  | 'convertedTotalAmount'
  | 'lineDescriptions'
  | 'note'
  | 'isPaid'
  | 'issueDate'
  | 'dueDate'
  | 'docNumber'
  | 'privateMerchantId'
  | 'leafCategoryId'
  | 'createdAt'
  | 'updatedAt'
> &
  OptionalPrivateMerchantAndLeafCategoryName;

export type BillAiDisplayQueryItem = Pick<
  Bill,
  | 'id'
  | 'amount'
  | 'currency'
  | 'convertedTotalAmount'
  | 'lineDescriptions'
  | 'note'
  | 'isPaid'
  | 'issueDate'
  | 'dueDate'
  | 'docNumber'
  | 'privateMerchantId'
  | 'leafCategoryId'
  | 'createdAt'
  | 'updatedAt'
> &
  OptionalPrivateMerchantAndLeafCategoryName;

export type CustomAiDisplayQueryItem = Record<string, unknown>;
