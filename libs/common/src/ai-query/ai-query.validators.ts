import { Enums } from '@compass/database';
import z, { ZodSchema } from 'zod';

import constants from './ai-query.constants';
import type {
  AiDisplayQueryType,
  BalanceAiDisplayQueryItem,
  BillAiDisplayQueryItem,
  DailyBalanceAiDisplayQueryItem,
  InvoiceAiDisplayQueryItem,
  TransactionAiDisplayQueryItem,
} from './ai-query.types';

const transactionAiDisplayQueryResponseValidator: ZodSchema<TransactionAiDisplayQueryItem> = z.object({
  id: z.number(),
  currency: z.nativeEnum(Enums.Currency),
  amount: z.number(),
  convertedAmount: z.record(z.nativeEnum(Enums.Currency), z.number()),
  paymentMethod: z.nativeEnum(Enums.PaymentMethod).nullable(),
  type: z.nativeEnum(Enums.FinancialType),
  date: z.date(),
  description: z.string().nullable(),
  accountNumber: z.string().nullable(),
  accountName: z.string().nullable(),
  bankAccountId: z.number(),
  privateMerchantId: z.number().nullable(),
  privateMerchantName: z.string().nullable(),
  leafCategoryId: z.number().nullable(),
  leafCategoryName: z.string().nullable(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

const balanceAiDisplayQueryResponseValidator: ZodSchema<BalanceAiDisplayQueryItem> = z.object({
  id: z.number(),
  displayName: z.string(),
  number: z.string().nullable(),
  currency: z.nativeEnum(Enums.Currency),
  currentBalance: z.number().nullable(),
  currentConvertedBalance: z.record(z.nativeEnum(Enums.Currency), z.number()).nullable(),
  date: z.date().nullable(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

const dailyBalanceAiDisplayQueryResponseValidator: ZodSchema<DailyBalanceAiDisplayQueryItem> = z.object({
  id: z.number(),
  balance: z.number(),
  convertedBalance: z.record(z.nativeEnum(Enums.Currency), z.number()),
  date: z.date(),
  bankAccountId: z.number(),
  displayName: z.string(),
  number: z.string().nullable(),
  currency: z.nativeEnum(Enums.Currency),
  createdAt: z.date(),
  updatedAt: z.date(),
});

const invoiceAiDisplayQueryResponseValidator: ZodSchema<InvoiceAiDisplayQueryItem> = z.object({
  id: z.number(),
  amount: z.number(),
  currency: z.nativeEnum(Enums.Currency),
  convertedTotalAmount: z.record(z.nativeEnum(Enums.Currency), z.number()),
  lineDescriptions: z.string(),
  docNumber: z.string(),
  note: z.string().nullable(),
  isPaid: z.boolean(),
  issueDate: z.date().nullable(),
  dueDate: z.date().nullable(),
  privateMerchantId: z.number().nullable(),
  privateMerchantName: z.string().nullable(),
  leafCategoryId: z.number().nullable(),
  leafCategoryName: z.string().nullable(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

const billAiDisplayQueryResponseValidator: ZodSchema<BillAiDisplayQueryItem> = z.object({
  id: z.number(),
  amount: z.number(),
  currency: z.nativeEnum(Enums.Currency),
  convertedTotalAmount: z.record(z.nativeEnum(Enums.Currency), z.number()),
  lineDescriptions: z.string(),
  docNumber: z.string(),
  note: z.string().nullable(),
  isPaid: z.boolean(),
  issueDate: z.date().nullable(),
  dueDate: z.date().nullable(),
  privateMerchantId: z.number().nullable(),
  privateMerchantName: z.string().nullable(),
  leafCategoryId: z.number().nullable(),
  leafCategoryName: z.string().nullable(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

async function validateAiDisplayQueryResponse(
  response: unknown,
  aiDisplayQueryType: AiDisplayQueryType,
  checkSize: boolean,
) {
  let validator: ZodSchema | null = null;

  switch (aiDisplayQueryType) {
    case constants.aiDisplayQueryType.transaction:
      validator = transactionAiDisplayQueryResponseValidator;
      break;

    case constants.aiDisplayQueryType.balance:
      validator = balanceAiDisplayQueryResponseValidator;
      break;

    case constants.aiDisplayQueryType.dailyBalance:
      validator = dailyBalanceAiDisplayQueryResponseValidator;
      break;

    case constants.aiDisplayQueryType.invoice:
      validator = invoiceAiDisplayQueryResponseValidator;
      break;

    case constants.aiDisplayQueryType.bill:
      validator = billAiDisplayQueryResponseValidator;
      break;
  }

  if (!validator) return response;

  if (checkSize) return validator.array().min(1).parseAsync(response);

  return validator.array().parseAsync(response);
}

export default { validateAiDisplayQueryResponse };
