import constants from './ai-query.constants';

const { aiQueryNamingMapper, aiDisplayQueryQueryNamingMapper } = constants;

const aiQueryRegex = new RegExp(Object.keys(aiQueryNamingMapper).join('|'), 'g');
const aDisplayQueryRegex = new RegExp(Object.keys(aiDisplayQueryQueryNamingMapper).join('|'), 'g');

function sanitizeAiQuery(sql: string) {
  return sql.replace(aiQueryRegex, (match) => aiQueryNamingMapper[match as keyof typeof aiQueryNamingMapper]);
}

function sanitizeAiDisplayQuery(sql: string) {
  return sql.replace(
    aDisplayQueryRegex,
    (match) => aiDisplayQueryQueryNamingMapper[match as keyof typeof aiDisplayQueryQueryNamingMapper],
  );
}

export default { sanitizeAiQuery, sanitizeAiDisplayQuery };
