const aiDisplayQueryType = {
  transaction: 'transaction',
  invoice: 'invoice',
  bill: 'bill',
  balance: 'balance',
  dailyBalance: 'dailyBalance',
  custom: 'custom',
};

const aiDisplayQueryValidatorType = {
  syntax: 'syntax',
  data: 'data',
} as const;

const aiQueryEnumsMapper = {
  FinancialType: '"FinancialType"',
  Currency: '"Currency"',
  PaymentMethod: '"PaymentMethod"',
  MerchantType: '"MerchantType"',
} as const;

const aiQueryNamingMapper = {
  '"Account"': '"PrivateMerchantForAiQuery"',
  '"accountId"': '"privateMerchantId"',
  '"Transaction"': '"TransactionForAiQuery"',
  '"BankAccount"': '"BankAccountForAiQuery"',
  '"BankAccountDailyBalance"': '"BankAccountDailyBalanceForAiQuery"',
  '"LeafCategory"': '"LeafCategoryForAiQuery"',
  '"Category"': '"CategoryForAiQuery"',
  ...aiQueryEnumsMapper,
} as const;

const aiDisplayQueryQueryNamingMapper = {
  '"Account"': '"PrivateMerchantForAiQuery"',
  '"accountId"': '"privateMerchantId"',
  '"Transaction"': '"TransactionForAiDisplayQuery"',
  '"BankAccount"': '"BankAccountForAiDisplayQuery"',
  '"BankAccountDailyBalance"': '"BankAccountDailyBalanceForAiDisplayQuery"',
  '"LeafCategory"': '"LeafCategoryForAiQuery"',
  '"Category"': '"CategoryForAiQuery"',
  '"Invoice"': '"InvoiceForAiDisplayQuery"',
  '"Bill"': '"BillForAiDisplayQuery"',
  ...aiQueryEnumsMapper,
} as const;

export default {
  aiDisplayQueryType,
  aiDisplayQueryValidatorType,
  aiQueryEnumsMapper,
  aiQueryNamingMapper,
  aiDisplayQueryQueryNamingMapper,
};
