import { aiDb } from '@compass/database';
import { Prisma } from '@prisma/client';

import type { Transaction } from '../types';
import constants from './ai-query.constants';
import type {
  ExecuteAiDisplayQueryArgs,
  ExecuteAiQueryAndReturnItemsArgs,
  ExecuteAiQueryAndReturnNumberOfRowsArgs,
  ValidateAiDisplayQueryArgs,
} from './ai-query.types';

function setCurrentCompanyId(companyId: number, tx: Transaction) {
  return tx.$executeRaw(Prisma.raw(`SET LOCAL app.currentCompanyId TO ${companyId}`));
}

function executeAiQueryAndReturnNumberOfRows({ sql, companyId, validate }: ExecuteAiQueryAndReturnNumberOfRowsArgs) {
  return aiDb.$transaction(async (tx) => {
    await setCurrentCompanyId(companyId, tx);
    return tx.$executeRaw(validate ? Prisma.raw(`EXPLAIN ${sql}`) : Prisma.raw(sql));
  });
}

function executeAiQueryAndReturnItems<T>({ sql, companyId }: ExecuteAiQueryAndReturnItemsArgs) {
  return aiDb.$transaction(async (tx) => {
    await setCurrentCompanyId(companyId, tx);
    return tx.$queryRaw<T>(Prisma.raw(sql));
  });
}

function validateAiDisplayQuery({ sql, companyId, type }: ValidateAiDisplayQueryArgs) {
  return aiDb.$transaction(async (tx) => {
    await setCurrentCompanyId(companyId, tx);
    return type === constants.aiDisplayQueryValidatorType.data ? tx.$queryRaw(sql) : tx.$executeRaw(sql);
  });
}

function executeAiDisplayQuery({ sql, companyId }: ExecuteAiDisplayQueryArgs) {
  return aiDb.$transaction(async (tx) => {
    await setCurrentCompanyId(companyId, tx);
    return tx.$queryRaw(Prisma.raw(sql));
  });
}

export default {
  executeAiQueryAndReturnNumberOfRows,
  executeAiQueryAndReturnItems,
  validateAiDisplayQuery,
  executeAiDisplayQuery,
};
