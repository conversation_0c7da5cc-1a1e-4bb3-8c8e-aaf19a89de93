import { emailHtmlTemplates } from '../html-templates';

function constructConnectionExpirationEmail(data: {
  name: string;
  title: string;
  paragraph1: string;
  paragraph2: string;
  actionUrl: string;
}) {
  const content = `
		<tr>
			<td style="padding: 0px 0px 32px 0px; text-align: center;">
				<span style="font-weight: 600; font-size: 20px; line-height: 28px">
				${data.title}
				</span>
			</td>
		</tr>
		<tr>
			<td style="height: 1px; background-color: #e5e7eb;"></td>
		</tr>
		<tr>
			<td style="padding: 24px 0px 16px 0px">
				<span style="font-weight: 400; font-size: 12px; line-height: 20px">
					Hello <strong>${data.name}</strong>,
					<br/><br/>
					${data.paragraph1}
				</span>
			</td>
		</tr>
		<tr>
			<td style="padding: 12px 0px 16px 0px">
				<span style="font-weight: 400; font-size: 12px; line-height: 20px">
					<strong>Why It Matters:</strong>
					<br/>
					${data.paragraph2}
				</span>
			</td>
		</tr>
		<tr>
			<td style="padding: 12px 0px 16px 0px">
				<span style="font-weight: 400; font-size: 12px; line-height: 20px">
					<strong>How to Reconnect:</strong>
					<br/>
					Navigate to Account Settings and click Reconnect:
				</span>
			</td>
		</tr>
		<tr>
			<td align="center" style="padding: 0px 0px 24px 0px">
				<a
				href="${data.actionUrl}"
				target="_blank"
				style="
				display: block;
				background-color: #4C3DAA;
				padding: 8px 12px;
				border: none;
				border-radius: 8px;
				color: #A2E4E2;
				font-weight: 600;
				font-size: 14px;
				line-height: 20px;
				text-decoration: none;
				"
				>Access Account Settings</a>
			</td>
		</tr>`;

  return emailHtmlTemplates.constructEmail(content);
}

export default { constructConnectionExpirationEmail };
