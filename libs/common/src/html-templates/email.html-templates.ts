import { configConstants } from '../constants';

function constructEmail(content: string) {
  return `
    <!doctype html>
    <html>
      <head>
        <meta http-equiv="Content-Type" content="text/html charset=UTF-8" />
        <style>
          body, table {
            font-family: Arial, sans-serif;
          }
        </style>
      </head>
      <body>
        <table
          style="
            padding: 48px 12px;
            border-spacing: 0px;
            width: 360px;
            background-color: #ffffff;
            margin: 0;
            color: #000000;
            margin: auto;
          "
        >
          <tbody>
            <tr>
              <td align="center" style="padding: 0px 0px 48px 0px">
                <img
                  src="${configConstants.aws.publicFilesBaseUrl}/media/compass-logo.png"
                  style="width: 200px"
                />
              </td>
            </tr>
            
            ${content}

            <tr>
              <td style="font-weight: 400px; font-size: 12px; line-height: 20px; padding: 0px 0px 24px 0px">
                <span>Happy navigating,</span>
                <br />
                <span>Compass team</span>
              </td>
            </tr>

            <tr>
              <td style="height: 1px; background-color: #e5e7eb; padding: 0px"></td>
            </tr>

            <tr>
              <td style="padding: 24px 0px 16px 0px">
                <a href="mailto:${configConstants.supportEmail}" target="_blank" style="text-decoration: none; margin-right: 16px">
                  <img
                    src="${configConstants.aws.publicFilesBaseUrl}/media/email-icon.png"
                    style="width: 16px"
                  />
                </a>

                <a
                  href="${process.env.WEB_APP_ORIGIN}/dashboard"
                  target="_blank"
                  style="text-decoration: none; margin-right: 16px"
                >
                  <img
                    src="${configConstants.aws.publicFilesBaseUrl}/media/web-icon.png"
                    style="width: 16px"
                  />
                </a>

                <a href="${configConstants.linkedinProfileUrl}" target="_blank" style="text-decoration: none">
                  <img
                    src="${configConstants.aws.publicFilesBaseUrl}/media/linkedin-icon.png"
                    style="width: 16px"
                  />
                </a>
              </td>
            </tr>

            <tr>
              <td style="padding: 0px">
                <span style="color: #6b7280; font-weight: 400px; font-size: 10px; line-height: 14px"
                  >© 2024 COMPASS. All rights reserved.</span
                >
                <br />
                <span style="color: #6b7280; font-weight: 400px; font-size: 10px; line-height: 14px"
                  >Created by the team behind Ars Futura.</span
                >
              </td>
            </tr>
          </tbody>
        </table>
      </body>
    </html>
  `;
}

export default { constructEmail };
