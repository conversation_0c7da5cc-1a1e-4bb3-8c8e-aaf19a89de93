import { Enums } from '@compass/database';

import { ConflictError } from '../helpers';
import repository from './category.repository';

async function validateCategory(companyId: number, categoryId: number | undefined, type: Enums.FinancialType) {
  if (!categoryId) return;

  const companyCategory = await repository.getCompanyCategory(companyId, categoryId);

  if (!companyCategory) throw new ConflictError('Category not found.', { categoryId });

  if (companyCategory.type !== type) {
    throw new ConflictError('Category is not of the same type.', { categoryId });
  }

  return companyCategory;
}

export default { validateCategory };
