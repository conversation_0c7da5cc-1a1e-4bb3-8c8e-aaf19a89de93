import type { Label } from '@prisma/client';
import type { Merge } from 'type-fest';

import { ConflictError } from '../helpers';
import repository from './label.repository';

async function validateLabelIds(companyId: number, labelIds: Array<number> | undefined) {
  if (!labelIds || labelIds.length === 0) return;

  const count = await repository.countLabelsByCompanyAndIds(companyId, labelIds);

  if (count !== labelIds.length) {
    throw new ConflictError('Some of the provided labels not found.', { companyId, labelIds });
  }
}

function transformLabelIdsForDb(labelIds: Array<number>) {
  return labelIds.map((labelId) => ({ label: { connect: { id: labelId } } }));
}

function transformItemWithLabelsRelation<T extends { labels: Array<{ label: Label }> }>(
  item: T,
): Merge<T, { labels: Array<Label> }> {
  return {
    ...item,
    labels: item.labels.map(({ label }) => label),
  };
}

export default { validateLabelIds, transformLabelIdsForDb, transformItemWithLabelsRelation };
