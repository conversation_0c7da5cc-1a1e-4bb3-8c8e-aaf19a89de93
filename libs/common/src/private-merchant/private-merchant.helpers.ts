import { ConflictError } from '../helpers';
import repository from './private-merchant.repository';

async function validatePrivateMerchant(companyId: number, privateMerchantId: number | null | undefined) {
  if (!privateMerchantId) return;

  const privateMerchant = await repository.getPrivateMerchant(companyId, privateMerchantId);

  if (!privateMerchant) throw new ConflictError('Private merchant not found.');
}

export default { validatePrivateMerchant };
