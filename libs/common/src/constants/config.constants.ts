const awsEnvironmentMapper = {
  local: 'local',
  development: 'dev',
  staging: 'staging',
  production: 'production',
} as const;
const awsEnv = awsEnvironmentMapper[process.env.ENVIRONMENT as keyof typeof awsEnvironmentMapper];

const environments = Object.keys(awsEnvironmentMapper) as ['local', 'development', 'staging', 'production'];

const awsAccountId = process.env.AWS_ACCOUNT_ID;
const awsRegion = process.env.AWS_REGION;

const linkedinProfileUrl = 'https://www.linkedin.com/company/compass-app-ai/';
const supportEmail = '<EMAIL>';

const saltEdgeUrl = 'https://www.saltedge.com/api/partners/v1';

const awsS3BucketRegionSuffixMapper = {
  'eu-central-1': '',
  'us-west-2': '-us',
} as const;
const awsS3BucketRegionSuffix = awsS3BucketRegionSuffixMapper[awsRegion as keyof typeof awsS3BucketRegionSuffixMapper];

const aws = {
  regions: { eu: 'eu-central-1', us: 'us-west-2' },

  sourceEmail: '<EMAIL>',

  financialInsightsDataS3Bucket: `compass-financial-insights-transactions-data-${awsEnv}${awsS3BucketRegionSuffix}`,
  alertNotificationsDataS3Bucket: `compass-alert-notifications-data-${awsEnv}${awsS3BucketRegionSuffix}`,
  companyFilesS3Bucket: `compass-company-files-${awsEnv}${awsS3BucketRegionSuffix}`,

  mockDataS3Bucket: 'compass-mock-data',

  publicFilesBaseUrl: 'https://compass-public-files-all-envs.s3.eu-central-1.amazonaws.com',

  entitiesSyncServiceLambdaArn: `arn:aws:lambda:${awsRegion}:${awsAccountId}:function:entities-sync-service-${awsEnv}`,
  entitiesSyncServiceSchedulerGroup: `entities-sync-${awsEnv}`,
  entitiesSyncServiceSchedulerRoleArn: `arn:aws:iam::${awsAccountId}:role/entities-sync-scheduler-${awsEnv}`,

  insightsServiceLambdaArn: `arn:aws:lambda:${awsRegion}:${awsAccountId}:function:insights-service-${awsEnv}`,
  insightsSchedulerGroup: `insights-schedule-${awsEnv}`,
  insightsSchedulerRoleArn: `arn:aws:iam::${awsAccountId}:role/insights-schedule-${awsEnv}`,

  monitoringServiceLambdaArn: `arn:aws:lambda:${awsRegion}:${awsAccountId}:function:monitoring-service-${awsEnv}`,
  monitoringSchedulerGroup: `monitoring-${awsEnv}`,
  monitoringSchedulerRoleArn: `arn:aws:iam::${awsAccountId}:role/monitoring-scheduler-${awsEnv}`,

  promptSuggestionServiceServiceLambdaArn: `arn:aws:lambda:${awsRegion}:${awsAccountId}:function:prompt-suggestions-service-${awsEnv}`,
  promptSuggestionServiceSchedulerGroup: `prompt-suggestions-${awsEnv}`,
  promptSuggestionServiceSchedulerRoleArn: `arn:aws:iam::${awsAccountId}:role/prompt-suggestions-scheduler-${awsEnv}`,
} as const;

const general = {
  environments,
  categorizationChunkSize: 10,
  categorizationSimilarityThreshold: 0.9,
  bankTransactionsPollTimeoutInSeconds: 300,
  entitiesSyncQuickbooksCaptureChangeDataHours: 12,
  numberOfUniqueTransactionsChecked: 50,
  longTransactionTimeoutMs: 60000,
} as const;

export default {
  linkedinProfileUrl,
  supportEmail,
  aws,
  general,
  saltEdgeUrl,
} as const;
