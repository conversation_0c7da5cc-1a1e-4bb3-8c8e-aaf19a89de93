import { Enums } from '@compass/database';
import { ExchangeRate } from '@prisma/client';

export type ExchangeRateWithoutBaseCurrency = Pick<ExchangeRate, 'targetCurrency' | 'rate' | 'reverseRate'>;

export type ExchangeRateDateWithTruncatedDateAndExchangeRate = {
  id: number;
  formattedTruncatedDate: string;
} & ExchangeRateWithoutBaseCurrency;

export type ExchangeRateDateWithTruncatedDateAndExchangeRates = {
  id: number;
  formattedTruncatedDate: string;
  exchangeRates: Array<ExchangeRateWithoutBaseCurrency>;
};

export type EarliestExchangeRateDate = { id: number; exchangeRates: Array<ExchangeRateWithoutBaseCurrency> };

export type ConvertedAmount = { [key in Enums.Currency]: number };
