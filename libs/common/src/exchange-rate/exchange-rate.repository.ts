import { db } from '@compass/database';
import { Prisma } from '@prisma/client';

import type {
  EarliestExchangeRateDate,
  ExchangeRateDateWithTruncatedDateAndExchangeRate,
  ExchangeRateDateWithTruncatedDateAndExchangeRates,
  ExchangeRateWithoutBaseCurrency,
} from './exchange-rate.types';

async function getExchangeRateDates(
  dateStrings: Array<string>,
): Promise<Array<ExchangeRateDateWithTruncatedDateAndExchangeRates>> {
  if (dateStrings.length === 0) return [];

  const data = await db.$queryRaw<Array<ExchangeRateDateWithTruncatedDateAndExchangeRate>>`
    SELECT 
      "ExchangeRateDate"."id", 
      TO_CHAR(DATE_TRUNC('day', "ExchangeRateDate"."date"), 'YYYY-MM-DD') AS "formattedTruncatedDate", 
      "ExchangeRate"."targetCurrency",
      "ExchangeRate"."rate",
      "ExchangeRate"."reverseRate"
    FROM "ExchangeRateDate" INNER JOIN "ExchangeRate" ON "ExchangeRate"."exchangeRateDateId" = "ExchangeRateDate"."id"
    WHERE DATE_TRUNC('day', "ExchangeRateDate"."date") = ANY(ARRAY[${Prisma.join(dateStrings)}]::TIMESTAMP[]);
  `;

  const groupedData: Record<number, ExchangeRateDateWithTruncatedDateAndExchangeRates> = {};

  for (const { id, formattedTruncatedDate, targetCurrency, rate, reverseRate } of data) {
    if (!groupedData[id]) {
      groupedData[id] = { id, formattedTruncatedDate, exchangeRates: [{ targetCurrency, rate, reverseRate }] };
    } else {
      groupedData[id].exchangeRates.push({ targetCurrency, rate, reverseRate });
    }
  }

  return Object.values(groupedData);
}

function getMostRecentExchangeRatesByDate(date: Date) {
  return db.$queryRaw<Array<ExchangeRateWithoutBaseCurrency>>`
    SELECT 
      "ExchangeRate"."targetCurrency", 
      "ExchangeRate"."rate",
      "ExchangeRate"."reverseRate"
    FROM "ExchangeRate" INNER JOIN "ExchangeRateDate" ON "ExchangeRate"."exchangeRateDateId" = "ExchangeRateDate"."id"
    WHERE "ExchangeRateDate"."id" = (
      SELECT "id" 
      FROM "ExchangeRateDate"
      WHERE DATE_TRUNC('day', "date") <= ${date}
      ORDER BY "date" DESC
      LIMIT 1
    )
  `;
}

function getEarliestExchangeRateDate(): Promise<EarliestExchangeRateDate | null> {
  return db.exchangeRateDate.findFirst({
    select: { id: true, exchangeRates: { select: { targetCurrency: true, rate: true, reverseRate: true } } },
    orderBy: { date: 'asc' },
  });
}

export default { getExchangeRateDates, getMostRecentExchangeRatesByDate, getEarliestExchangeRateDate };
