import { Enums } from '@compass/database';
import dayjs from 'dayjs';

import helpers from './exchange-rate.helpers';
import repository from './exchange-rate.repository';
import type {
  ExchangeRateDateWithTruncatedDateAndExchangeRates,
  ExchangeRateWithoutBaseCurrency,
} from './exchange-rate.types';

function getExchangeRateDates(dateStrings: Array<Date>) {
  const uniqueDates = [...new Set(dateStrings.map((date) => helpers.transformToUtcAndTruncateDay(date).toISOString()))];

  return repository.getExchangeRateDates(uniqueDates);
}

function getMostRecentExchangeRatesByDate(date: Date) {
  const transformedDate = helpers.transformToUtcAndTruncateDay(date);

  return repository.getMostRecentExchangeRatesByDate(transformedDate);
}

async function calculateConvertedAmountWithLookup(
  exchangeRateDates: Array<ExchangeRateDateWithTruncatedDateAndExchangeRates>,
  date: Date,
  amount: number,
  currency: Enums.Currency,
) {
  const transformedDate = helpers.transformToUtcAndTruncateDay(date);

  const formattedDate = dayjs(transformedDate).format('YYYY-MM-DD');

  const exchangeRates = await helpers.findExchangeRates(exchangeRateDates, transformedDate, formattedDate);

  return helpers.calculateConvertedAmount(exchangeRates, formattedDate, amount, currency);
}

function calculateConvertedAmountFromExchangeRates(
  exchangeRates: Array<ExchangeRateWithoutBaseCurrency>,
  date: Date,
  amount: number,
  currency: Enums.Currency,
) {
  const formattedDate = dayjs(date).format('YYYY-MM-DD');

  return helpers.calculateConvertedAmount(exchangeRates, formattedDate, amount, currency);
}

export default {
  getExchangeRateDates,
  getMostRecentExchangeRatesByDate,
  calculateConvertedAmountWithLookup,
  calculateConvertedAmountFromExchangeRates,
};
