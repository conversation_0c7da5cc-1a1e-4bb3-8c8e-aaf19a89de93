import { Enums } from '@compass/database';
import _ from 'lodash';

import repository from './exchange-rate.repository';
import type {
  ConvertedAmount,
  ExchangeRateDateWithTruncatedDateAndExchangeRates,
  ExchangeRateWithoutBaseCurrency,
} from './exchange-rate.types';

const currenciesWithoutEur = Object.values(_.omit(Enums.Currency, Enums.Currency.EUR));

async function findExchangeRates(
  exchangeRateDates: Array<ExchangeRateDateWithTruncatedDateAndExchangeRates>,
  date: Date,
  formattedDate: string,
) {
  const exchangeRates = exchangeRateDates.find(
    (exchangeRateDate) => exchangeRateDate.formattedTruncatedDate === formattedDate,
  )?.exchangeRates;
  if (exchangeRates) return exchangeRates;

  const mostRecentExchangeRates = await repository.getMostRecentExchangeRatesByDate(date);
  if (mostRecentExchangeRates.length > 0) return mostRecentExchangeRates;

  const earliestExchangeRates = (await repository.getEarliestExchangeRateDate())?.exchangeRates;
  if (earliestExchangeRates) return earliestExchangeRates;

  throw new Error('No exchange rates in database');
}

function convertAmountToEur(
  exchangeRates: Array<ExchangeRateWithoutBaseCurrency>,
  formattedDate: string,
  amount: number,
  currency: Enums.Currency,
) {
  if (currency === Enums.Currency.EUR) return amount;

  const exchangeRateForEurConversion = exchangeRates.find((exchangeRate) => exchangeRate.targetCurrency === currency);
  if (!exchangeRateForEurConversion) {
    throw new Error(`Exchange rate missing for ${currency} and ${formattedDate}`);
  }

  return amount * exchangeRateForEurConversion.reverseRate;
}

function calculateConvertedAmount(
  exchangeRates: Array<ExchangeRateWithoutBaseCurrency>,
  formattedDate: string,
  amount: number,
  currency: Enums.Currency,
) {
  const amountInEur = convertAmountToEur(exchangeRates, formattedDate, amount, currency);

  const convertedAmount = { [Enums.Currency.EUR]: amountInEur } as ConvertedAmount;

  for (const targetCurrency of currenciesWithoutEur) {
    const exchangeRate = exchangeRates.find((exchangeRate) => exchangeRate.targetCurrency === targetCurrency);
    if (!exchangeRate) {
      throw new Error(`Exchange rate missing for ${targetCurrency} and ${formattedDate}`);
    }

    if (currency !== targetCurrency) {
      convertedAmount[targetCurrency] = amountInEur * exchangeRate.rate;
    } else {
      convertedAmount[targetCurrency] = amount;
    }
  }

  return convertedAmount;
}

function transformToUtcAndTruncateDay(date: Date) {
  return new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()));
}

export default { findExchangeRates, calculateConvertedAmount, transformToUtcAndTruncateDay };
