# Mock data documentation

Mock data is data that is shown to the user after they onboard and before they connect their bank or accounting system.

To get the data that is relevant to the current date we should run this process a few times a year.

## How to generate mock data

Easiest way to get mock data is to complete onboarding locally with all transactions (`transactions-data.json`). This can be changed in `mock-data.seed.ts` by changing fetching from S3 to reading from file system. Before doing that `prepareTransactions` function should be updated to skip transactions that are after the current date. Just add

```ts
if (adjustedDate > new Date()) continue;
```

inside the for loop. After transactions have been imported until the current date forecast needs to be run. Check that all data makes sense and run two SQL queries below to get the data that will be used in the mock data.

This new data should follow the same structure as the existing `jun-16` in the `compass-mock-data` bucket. After a new structure is created the array inside `getFileDate` should be updated.

## SQL Queries

### Transactions query

```sql
SELECT jsonb_build_object('accounts', jsonb_agg("account")) AS "result"
FROM (
  SELECT jsonb_build_object(
    'accountName', "PrivateMerchant"."displayName",
    'type', "PrivateMerchant"."type",
    'transactions', jsonb_agg(
      jsonb_build_object(
        'amount', "Transaction"."amount",
        'paymentMethod', "Transaction"."paymentMethod",
        'type', "Transaction"."type",
        'date', to_char("Transaction"."date", 'YYYY-MM-DD'),
        'description', "Transaction"."description",
        'ownerAccountNumber', "Transaction"."ownerAccountNumber",
        'ownerAccountName', "Transaction"."ownerAccountName",
        'accountNumber', "Transaction"."accountNumber",
        'accountName', "Transaction"."accountName",
        'leafCategoryName', "LeafCategory"."name"
      )
    )
  ) AS "account"
  FROM "PrivateMerchant"
  JOIN "Transaction" ON "Transaction"."privateMerchantId" = "PrivateMerchant"."id"
  LEFT JOIN "LeafCategory" ON "Transaction"."leafCategoryId" = "LeafCategory"."id"
  WHERE "Transaction"."companyId" = ${companyId}
  GROUP BY "PrivateMerchant"."displayName", "PrivateMerchant"."type"
) "accounts";
```

### Forecast query

```sql
WITH "context" AS (
  SELECT
    "id",
    to_char((SELECT MIN("date") FROM "Transaction" WHERE "companyId" = ${companyId}), 'MM-YYYY') AS "startPeriod",
    "endPeriod"
  FROM "Context"
  WHERE "companyId" = ${companyId}
  ORDER BY "id"
  LIMIT 1
),
"forecast" AS (
  SELECT "id", "startPeriod", "endPeriod", "isBase", 'Smart Forecast' AS "name"
  FROM "Forecast"
  WHERE "companyId" = ${companyId} AND "isBase" = FALSE
  ORDER BY "id"
  LIMIT 1
),
"baseForecast" AS (
  SELECT "id", "startPeriod", "endPeriod", "isBase", 'Base Scenario' AS "name"
  FROM "Forecast"
  WHERE "companyId" = ${companyId} AND "isBase" = TRUE
  ORDER BY "id"
  LIMIT 1
),
"accountForecasts" AS (
  SELECT
    "AccountForecast"."privateMerchantName",
    "AccountForecast"."majorAssumptions",
    "AccountForecast"."keyRisks",
    "AccountForecast"."reasoning",
    (
      SELECT jsonb_agg(
        jsonb_build_object(
          'period', "AccountMonthlyForecast"."period",
          'amount', "AccountMonthlyForecast"."amount",
          'reasoning', "AccountMonthlyForecast"."reasoning",
          'leafCategoryName', "LeafCategory"."name"
        )
        ORDER BY "AccountMonthlyForecast"."period"
      )
      FROM "AccountMonthlyForecast"
      LEFT JOIN "LeafCategory" ON "AccountMonthlyForecast"."leafCategoryId" = "LeafCategory"."id"
      WHERE "AccountMonthlyForecast"."forecastId" = "AccountForecast"."id"
    ) AS "monthlyForecasts"
  FROM "AccountForecast"
  WHERE "AccountForecast"."forecastId" = (SELECT "id" FROM "forecast")
  ORDER BY "AccountForecast"."privateMerchantName"
),
"baseAccountForecasts" AS (
  SELECT
    "AccountForecast"."privateMerchantName",
    "AccountForecast"."majorAssumptions",
    "AccountForecast"."keyRisks",
    "AccountForecast"."reasoning",
    (
      SELECT jsonb_agg(
        jsonb_build_object(
          'period', "AccountMonthlyForecast"."period",
          'amount', "AccountMonthlyForecast"."amount",
          'reasoning', "AccountMonthlyForecast"."reasoning",
          'leafCategoryName', "LeafCategory"."name"
        )
        ORDER BY "AccountMonthlyForecast"."period"
      )
      FROM "AccountMonthlyForecast"
      LEFT JOIN "LeafCategory" ON "AccountMonthlyForecast"."leafCategoryId" = "LeafCategory"."id"
      WHERE "AccountMonthlyForecast"."forecastId" = "AccountForecast"."id"
    ) AS "monthlyForecasts"
  FROM "AccountForecast"
  WHERE "AccountForecast"."forecastId" = (SELECT "id" FROM "baseForecast")
  ORDER BY "AccountForecast"."privateMerchantName"
),
"accountContexts" AS (
  SELECT
    "PrivateMerchant"."displayName" as "privateMerchantName",
    "AccountContext"."accountStatus",
    "AccountContext"."accountSummary",
    "AccountContext"."accountSummaryReasoning",
    "AccountContext"."lastActivityDate",
    "AccountContext"."keyRisks",
    "AccountContext"."primaryDrivers",
    "AccountContext"."reasoning",
    (
      SELECT jsonb_agg(
        jsonb_build_object(
          'notes', "AccountRecurringPattern"."notes",
          'amount', "AccountRecurringPattern"."amount",
          'leafCategoryName', "LeafCategory"."name",
          'frequency', "AccountRecurringPattern"."frequency",
          'scopeType', "AccountRecurringPattern"."scopeType",
          'scopeLeafCategoryNames', (
            SELECT jsonb_agg("LeafCategory"."name")
            FROM "LeafCategory"
            WHERE "LeafCategory"."id" = ANY("AccountRecurringPattern"."scopeLeafCategoryIds")
          ),
          'dayVariance', "AccountRecurringPattern"."dayVariance",
          'lastObserved', "AccountRecurringPattern"."lastObserved",
          'firstObserved', "AccountRecurringPattern"."firstObserved",
          'amountVariance', "AccountRecurringPattern"."amountVariance",
          'occurrenceCount', "AccountRecurringPattern"."occurrenceCount",
          'scopeDescription', "AccountRecurringPattern"."scopeDescription",
          'typicalDayOfMonth', "AccountRecurringPattern"."typicalDayOfMonth"
        )
        ORDER BY "AccountRecurringPattern"."id"
      )
      FROM "AccountRecurringPattern"
      JOIN "LeafCategory" ON "AccountRecurringPattern"."leafCategoryId" = "LeafCategory"."id"
      WHERE "AccountRecurringPattern"."contextId" = "AccountContext"."id"
    ) AS "recurringPatterns",
    (
      SELECT jsonb_agg(
        jsonb_build_object(
          'notes', "AccountSeasonality"."notes",
          'leafCategoryName', "LeafCategory"."name",
          'scopeType', "AccountSeasonality"."scopeType",
          'peakPeriods', "AccountSeasonality"."peakPeriods",
          'troughPeriods', "AccountSeasonality"."troughPeriods",
          'scopeLeafCategoryNames', (
            SELECT jsonb_agg("LeafCategory"."name")
            FROM "LeafCategory"
            WHERE "LeafCategory"."id" = ANY("AccountSeasonality"."scopeLeafCategoryIds")
          ),
          'scopeDescription', "AccountSeasonality"."scopeDescription",
          'typicalAmount', "AccountSeasonality"."typicalAmount",
          'amountVariance', "AccountSeasonality"."amountVariance",
          'peakMultiplier', "AccountSeasonality"."peakMultiplier",
          'cyclesObserved', "AccountSeasonality"."cyclesObserved"
        )
        ORDER BY "AccountSeasonality"."id"
      )
      FROM "AccountSeasonality"
      JOIN "LeafCategory" ON "AccountSeasonality"."leafCategoryId" = "LeafCategory"."id"
      WHERE "AccountSeasonality"."contextId" = "AccountContext"."id"
    ) AS "seasonalities",
    (
      SELECT jsonb_agg(
        jsonb_build_object(
          'notes', "AccountTrend"."notes",
          'leafCategoryName', "LeafCategory"."name",
          'scopeType', "AccountTrend"."scopeType",
          'trendType', "AccountTrend"."trendType",
          'scopeLeafCategoryNames', (
            SELECT jsonb_agg("LeafCategory"."name")
            FROM "LeafCategory"
            WHERE "LeafCategory"."id" = ANY("AccountTrend"."scopeLeafCategoryIds")
          ),
          'growthRate', "AccountTrend"."growthRate",
          'volatility', "AccountTrend"."volatility",
          'lastActivePeriod', "AccountTrend"."lastActivePeriod",
          'scopeDescription', "AccountTrend"."scopeDescription",
          'trendStartPeriod', "AccountTrend"."trendStartPeriod"
        )
        ORDER BY "AccountTrend"."id"
      )
      FROM "AccountTrend"
      JOIN "LeafCategory" ON "AccountTrend"."leafCategoryId" = "LeafCategory"."id"
      WHERE "AccountTrend"."contextId" = "AccountContext"."id"
    ) AS "trends"
  FROM "AccountContext"
  JOIN "PrivateMerchant" ON "AccountContext"."privateMerchantId" = "PrivateMerchant"."id"
  WHERE "AccountContext"."contextId" = (SELECT "id" FROM "context")
  ORDER BY "AccountContext"."privateMerchantId"
)
SELECT jsonb_build_object(
  'context', (SELECT row_to_json("context") FROM "context"),
  'accountContexts', (SELECT jsonb_agg("accountContexts") FROM "accountContexts"),
  'baseForecast', (SELECT row_to_json("baseForecast") FROM "baseForecast"),
  'baseAccountForecasts', (SELECT jsonb_agg("baseAccountForecasts") FROM "baseAccountForecasts"),
  'forecast', (SELECT row_to_json("forecast") FROM "forecast"),
  'accountForecasts', (SELECT jsonb_agg("accountForecasts") FROM "accountForecasts")
) AS "result"
```
