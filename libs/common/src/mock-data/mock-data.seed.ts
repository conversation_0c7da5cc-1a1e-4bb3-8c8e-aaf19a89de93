import fs from 'fs';
import path from 'path';

import type { Transaction } from '../../types';
import { configConstants } from '../constants';
import { InternalServerError } from '../helpers';
import { s3Service } from '../services/aws';
import helpers from './mock-data.helpers';
import repository from './mock-data.repository';
import type { MockForecastData, MockPromptSuggestionsData, MockTransactionsData } from './mock-data.types';

const mockPromptSuggestionsData: MockPromptSuggestionsData = JSON.parse(
  fs.readFileSync(path.join(__dirname, './data/prompt-suggestions-data.json'), 'utf8'),
);

async function seed({ companyId, isSeed }: { companyId: number; isSeed: boolean }, tx: Transaction): Promise<void> {
  const currentDate = new Date();
  const yearOffset = currentDate.getFullYear() - 2025; // In JSON, 2025 is the latest year

  const fileDate = helpers.getFileDate();

  const [mockTransactionsData, mockForecastData] = (await Promise.all([
    s3Service.getObjectAsJson(configConstants.aws.mockDataS3Bucket, `${fileDate}/transaction-data.json`),
    s3Service.getObjectAsJson(configConstants.aws.mockDataS3Bucket, `${fileDate}/forecast-data.json`),
  ])) as [MockTransactionsData, MockForecastData];

  const company = await repository.getCompany(companyId, tx);
  if (!company) throw new InternalServerError('Company not found', { companyId });

  const { currency } = company;

  const { id: bankAccountId } = await repository.createBankAccount(companyId, currency, tx);

  const privateMerchants = await repository.createPrivateMerchants(companyId, mockTransactionsData, tx);

  const privateMerchantMap = new Map(privateMerchants.map(({ displayName, id }) => [displayName, id]));
  const leafCategoryMap = new Map(company.leafCategories.map(({ name, id }) => [name, id]));

  const transactions = await repository.createTransactions(
    helpers.prepareTransactions(
      company,
      bankAccountId,
      mockTransactionsData,
      privateMerchantMap,
      leafCategoryMap,
      yearOffset,
    ),
    tx,
  );

  const { currentBalance, currentConvertedBalance, bankAccountDailyBalancesToCreate } = helpers.calculateDailyBalances(
    companyId,
    bankAccountId,
    transactions,
  );

  await repository.createBankAccountDailyBalances(bankAccountDailyBalancesToCreate, tx);

  await repository.updateBankAccount(bankAccountId, currentBalance, currentConvertedBalance, tx);

  await repository.createForecast(company, isSeed, mockForecastData, privateMerchantMap, leafCategoryMap, tx);

  await repository.createPromptSuggestions(helpers.preparePromptSuggestions(companyId, mockPromptSuggestionsData), tx);
}

export default seed;
