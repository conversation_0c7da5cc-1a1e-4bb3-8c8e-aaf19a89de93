import { logger } from '@compass/common';
import type { ExchangeRate } from '@compass/common/types';
import { Enums } from '@compass/database';
import { Prisma } from '@prisma/client';
import dayjs from 'dayjs';

import type { Company, DateTransaction, MockPromptSuggestionsData, MockTransactionsData } from './mock-data.types';

function getPlaceholderConvertedAmount(amount: number) {
  return Object.keys(Enums.Currency).reduce(
    (acc, currency) => {
      acc[currency] = amount;
      return acc;
    },
    {} as Record<string, number>,
  ) as ExchangeRate.ConvertedAmount;
}

function prepareTransactions(
  company: Company,
  bankAccountId: number,
  mockTransactionsData: MockTransactionsData,
  privateMerchantMap: Map<string, number>,
  leafCategoryMap: Map<string, number>,
  yearOffset: number,
) {
  const transactionsToCreate: Prisma.TransactionCreateManyArgs['data'] = [];

  for (const account of mockTransactionsData.accounts) {
    for (const transaction of account.transactions) {
      const adjustedDate = new Date(transaction.date);
      adjustedDate.setFullYear(adjustedDate.getFullYear() + yearOffset);

      const leafCategoryId = leafCategoryMap.get(transaction.leafCategoryName);
      if (!leafCategoryId) logger.info(`Leaf category not found - ${transaction.leafCategoryName}`);

      const { amount } = transaction;

      transactionsToCreate.push({
        companyId: company.id,
        leafCategoryId,
        privateMerchantId: privateMerchantMap.get(account.accountName),
        bankAccountId,
        currency: company.currency,
        type: transaction.type,
        date: adjustedDate,
        amount,
        convertedAmount: getPlaceholderConvertedAmount(amount),
        paymentMethod: transaction.paymentMethod,
        description: transaction.description,
        ownerAccountNumber: transaction.ownerAccountNumber,
        ownerAccountName: transaction.ownerAccountName,
        accountNumber: transaction.accountNumber,
        accountName: transaction.accountName,
      });
    }
  }

  return transactionsToCreate;
}

function calculateDailyBalances(companyId: number, bankAccountId: number, transactions: Array<DateTransaction>) {
  let currentBalance = 100000;

  transactions.sort((a, b) => a.date.getTime() - b.date.getTime());

  const startDate = new Date(transactions[0].date);

  const balanceStartDate = new Date(startDate);
  balanceStartDate.setDate(balanceStartDate.getDate() - 1);

  const bankAccountDailyBalancesToCreate: Prisma.BankAccountDailyBalanceCreateManyArgs['data'] = [
    {
      companyId,
      bankAccountId,
      date: balanceStartDate,
      balance: currentBalance,
      convertedBalance: getPlaceholderConvertedAmount(currentBalance),
    },
  ];

  const transactionsByDateMap = new Map<string, Array<DateTransaction>>();
  const start = dayjs(startDate).startOf('day');
  const end = dayjs(transactions[transactions.length - 1].date).startOf('day');

  for (let date = start; date.isBefore(end) || date.isSame(end); date = date.add(1, 'day')) {
    transactionsByDateMap.set(date.format('YYYY-MM-DD'), []);
  }

  for (const transaction of transactions) {
    const dateString = transaction.date.toISOString().split('T')[0];
    transactionsByDateMap.get(dateString)!.push(transaction);
  }

  const sortedDates = Array.from(transactionsByDateMap.keys()).sort();

  for (const dateString of sortedDates) {
    const dayTransactions = transactionsByDateMap.get(dateString) || [];

    currentBalance += dayTransactions.reduce((sum, { amount }) => sum + amount, 0);

    bankAccountDailyBalancesToCreate.push({
      companyId,
      bankAccountId,
      date: new Date(dateString),
      balance: currentBalance,
      convertedBalance: getPlaceholderConvertedAmount(currentBalance),
    });
  }

  return {
    currentBalance,
    currentConvertedBalance: getPlaceholderConvertedAmount(currentBalance),
    bankAccountDailyBalancesToCreate,
  };
}

function preparePromptSuggestions(companyId: number, mockPromptSuggestionsData: MockPromptSuggestionsData) {
  return [
    ...mockPromptSuggestionsData.widgetPromptSuggestions.map((prompt) => ({
      companyId,
      prompt,
      type: Enums.PromptSuggestionType.WIDGET,
    })),
    ...mockPromptSuggestionsData.aiAssistantPromptSuggestions.map((prompt) => ({
      companyId,
      prompt,
      type: Enums.PromptSuggestionType.AI_ASSISTANT,
    })),
  ];
}

function getFileDate() {
  const fileDates = ['jun-16'] as const;
  const now = dayjs();

  const parsed = fileDates.map((label) => {
    const [mon, day] = label.split('-');
    const date = dayjs(`${now.year()}-${mon}-${day}`, 'YYYY-MMM-DD');
    return { date, label };
  });

  const filtered = parsed.filter(({ date }) => date.isSame(now, 'day') || date.isBefore(now, 'day'));

  if (filtered.length === 0) return parsed[parsed.length - 1].label;

  return filtered[filtered.length - 1].label;
}

export default {
  prepareTransactions,
  calculateDailyBalances,
  preparePromptSuggestions,
  getFileDate,
};
