import { Enums } from '@compass/database';
import { Prisma } from '@prisma/client';
import _ from 'lodash';

import type { ExchangeRate, Transaction } from '../../types';
import { mockDataConstants } from '../constants';
import logger from '../logger';
import { compassAiService } from '../services';
import type {
  Company,
  MockAccountForecast,
  MockForecastData,
  MockTransactionsData,
  PrivateMerchant,
} from './mock-data.types';

async function getCompany(companyId: number, tx: Transaction): Promise<Company | null> {
  const company = await tx.company.findUnique({
    where: { id: companyId },
    select: {
      id: true,
      name: true,
      currency: true,
      industry: { select: { name: true } },
      country: { select: { name: true } },
      leafCategories: { select: { name: true, id: true } },
    },
  });

  if (!company) return null;

  return {
    id: company.id,
    name: company.name,
    currency: company.currency,
    industry: company.industry.name,
    country: company.country.name,
    leafCategories: company.leafCategories,
  };
}

function createBankAccount(companyId: number, currency: Enums.Currency, tx: Transaction) {
  return tx.bankAccount.create({
    data: {
      companyId,
      currency,
      isDefault: true,
      name: mockDataConstants.bankAccountName,
      displayName: 'Main Bank Account',
    },
  });
}

function createPrivateMerchants(
  companyId: number,
  mockTransactionsData: MockTransactionsData,
  tx: Transaction,
): Promise<Array<PrivateMerchant>> {
  const uniquePrivateMerchants = Array.from(
    new Map(mockTransactionsData.accounts.map((account) => [account.accountName, account])).values(),
  );
  return tx.privateMerchant.createManyAndReturn({
    select: { id: true, displayName: true },
    data: uniquePrivateMerchants.map(({ accountName, type }) => ({ displayName: accountName, type, companyId })),
  });
}

function createTransactions(data: Prisma.TransactionCreateManyArgs['data'], tx: Transaction) {
  return tx.transaction.createManyAndReturn({ select: { date: true, amount: true }, data });
}

function createBankAccountDailyBalances(data: Prisma.BankAccountDailyBalanceCreateManyArgs['data'], tx: Transaction) {
  return tx.bankAccountDailyBalance.createMany({ data });
}

function createPromptSuggestions(data: Prisma.PromptSuggestionCreateManyArgs['data'], tx: Transaction) {
  return tx.promptSuggestion.createMany({ data });
}

function updateBankAccount(
  bankAccountId: number,
  currentBalance: number,
  currentConvertedBalance: ExchangeRate.ConvertedAmount,
  tx: Transaction,
) {
  return tx.bankAccount.update({
    where: { id: bankAccountId },
    data: { currentBalance, currentConvertedBalance },
  });
}

function createAccountForecast(
  forecastId: number,
  privateMerchantId: number | undefined,
  accountForecast: MockAccountForecast,
  leafCategoryMap: Map<string, number>,
  tx: Transaction,
) {
  return tx.accountForecast.create({
    select: { id: true },
    data: {
      forecast: { connect: { id: forecastId } },
      privateMerchant: privateMerchantId ? { connect: { id: privateMerchantId } } : undefined,
      privateMerchantName: accountForecast.privateMerchantName,
      keyRisks: accountForecast.keyRisks ?? [],
      majorAssumptions: accountForecast.majorAssumptions ?? [],
      reasoning: accountForecast.reasoning,
      monthlyForecasts: {
        createMany: {
          data: (accountForecast.monthlyForecasts ?? [])?.map((a) => ({
            amount: a.amount,
            period: a.period,
            reasoning: a.reasoning,
            leafCategoryId: leafCategoryMap.get(a.leafCategoryName) ?? null,
          })),
        },
      },
    },
  });
}

async function createForecast(
  company: Company,
  isSeed: boolean,
  mockForecastData: MockForecastData,
  privateMerchantMap: Map<string, number>,
  leafCategoryMap: Map<string, number>,
  tx: Transaction,
) {
  const { id: companyId, currency } = company;

  const context = await tx.context.create({
    select: { id: true, forecasts: { select: { id: true, isBase: true } } },
    data: {
      companyId,
      currency,
      startPeriod: mockForecastData.context.startPeriod,
      endPeriod: mockForecastData.context.endPeriod,
      forecasts: {
        createMany: {
          data: [
            {
              companyId,
              startPeriod: mockForecastData.baseForecast.startPeriod,
              endPeriod: mockForecastData.baseForecast.endPeriod,
              isBase: mockForecastData.baseForecast.isBase,
              name: mockForecastData.baseForecast.name,
            },
            {
              companyId,
              startPeriod: mockForecastData.forecast.startPeriod,
              endPeriod: mockForecastData.forecast.endPeriod,
              isBase: mockForecastData.forecast.isBase,
              name: mockForecastData.forecast.name,
            },
          ],
        },
      },
    },
  });

  for (const accountContext of mockForecastData.accountContexts) {
    await tx.accountContext.create({
      select: { id: true },
      data: {
        contextId: context.id,
        privateMerchantId: privateMerchantMap.get(accountContext.privateMerchantName)!,
        accountStatus: accountContext.accountStatus,
        accountSummary: accountContext.accountSummary,
        accountSummaryReasoning: accountContext.accountSummaryReasoning,
        lastActivityDate: accountContext.lastActivityDate,
        keyRisks: accountContext.keyRisks,
        primaryDrivers: accountContext.primaryDrivers,
        reasoning: accountContext.reasoning,
        recurringPatterns: {
          createMany: {
            data: (accountContext.recurringPatterns ?? []).map((recurringPatterns) => {
              return {
                ..._.omit(recurringPatterns, ['leafCategoryName', 'scopeLeafCategoryNames']),
                scopeLeafCategoryIds: (recurringPatterns.scopeLeafCategoryNames ?? [])
                  .map((name) => leafCategoryMap.get(name))
                  .filter(Boolean) as Array<number>,
                leafCategoryId: leafCategoryMap.get(recurringPatterns.leafCategoryName) ?? null,
              };
            }),
          },
        },
        seasonalities: {
          createMany: {
            data: (accountContext.seasonalities ?? []).map((seasonality) => ({
              ..._.omit(seasonality, ['leafCategoryName', 'scopeLeafCategoryNames']),
              scopeLeafCategoryIds: (seasonality.scopeLeafCategoryNames ?? [])
                .map((name) => leafCategoryMap.get(name))
                .filter(Boolean) as Array<number>,
              leafCategoryId: leafCategoryMap.get(seasonality.leafCategoryName) ?? null,
            })),
          },
        },
        trends: {
          createMany: {
            data: (accountContext.trends ?? []).map((trend) => ({
              ..._.omit(trend, ['leafCategoryName', 'scopeLeafCategoryNames']),
              scopeLeafCategoryIds: (trend.scopeLeafCategoryNames ?? [])
                .map((name) => leafCategoryMap.get(name))
                .filter(Boolean) as Array<number>,
              leafCategoryId: leafCategoryMap.get(trend.leafCategoryName) ?? null,
            })),
          },
        },
      },
    });
  }

  const baseForecastId = context.forecasts.find((f) => f.isBase === true)!.id;
  const forecastId = context.forecasts.find((f) => f.isBase === false)!.id;

  for (const accountForecast of mockForecastData.accountForecasts) {
    const privateMerchantId = privateMerchantMap.get(accountForecast.privateMerchantName);

    await createAccountForecast(forecastId, privateMerchantId, accountForecast, leafCategoryMap, tx);
  }

  for (const accountForecast of mockForecastData.baseAccountForecasts) {
    const privateMerchantId = privateMerchantMap.get(accountForecast.privateMerchantName);

    await createAccountForecast(baseForecastId, privateMerchantId, accountForecast, leafCategoryMap, tx);
  }

  /** Skip creating initial nodes for seeded company. Don't want the seed to depend on AI. Comment the condition out when needed */
  if (!isSeed) {
    await compassAiService
      .createInitialWorkflowNodes({
        company: {
          id: companyId,
          name: company.name,
          country: company.country,
          industry: company.industry,
          defaultCurrency: currency,
        },
        forecast_id: forecastId,
        current_node_id: null,
      })
      .catch((error) => logger.error({ companyId, error }, 'Failed to create initial Compass AI nodes'));
  }
}

export default {
  getCompany,
  createBankAccount,
  createPrivateMerchants,
  createTransactions,
  createBankAccountDailyBalances,
  createPromptSuggestions,
  updateBankAccount,
  createForecast,
};
