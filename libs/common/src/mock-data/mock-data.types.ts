import { Enums } from '@compass/database';
import type { Transaction } from '@prisma/client';

export type Company = {
  id: number;
  name: string;
  currency: Enums.Currency;
  industry: string;
  country: string;
  leafCategories: Array<{ id: number; name: string }>;
};

export type PrivateMerchant = { id: number; displayName: string };

export type MockTransactionsData = {
  accounts: Array<{
    accountName: string;
    type: Enums.MerchantType;
    transactions: Array<Transaction & { leafCategoryName: string; date: string }>;
  }>;
};

export type MockPromptSuggestionsData = {
  widgetPromptSuggestions: Array<string>;
  aiAssistantPromptSuggestions: Array<string>;
};

type MockForecast = { name: string; endPeriod: string; startPeriod: string; isBase: boolean };

export type MockAccountForecast = {
  keyRisks: Array<string>;
  reasoning: string;
  majorAssumptions: Array<string>;
  privateMerchantName: string;
  monthlyForecasts: Array<{ amount: number; period: string; reasoning: string; leafCategoryName: string }> | null;
};

export type MockForecastData = {
  forecast: MockForecast;
  accountForecasts: Array<MockAccountForecast>;
  baseForecast: MockForecast;
  baseAccountForecasts: Array<MockAccountForecast>;
  context: { endPeriod: string; startPeriod: string };
  accountContexts: Array<{
    privateMerchantName: string;
    accountStatus: Enums.AccountStatus;
    accountSummary: string;
    accountSummaryReasoning: string;
    lastActivityDate: string;
    keyRisks: Array<string>;
    primaryDrivers: Array<string>;
    reasoning: string;
    trends: Array<{
      notes: string;
      leafCategoryName: string;
      scopeType: Enums.PatternScopeType;
      trendType: Enums.TrendType;
      scopeLeafCategoryNames: Array<string>;
      growthRate: number | null;
      volatility: number | null;
      lastActivePeriod: string | null;
      scopeDescription: string | null;
      trendStartPeriod: string | null;
    }>;
    seasonalities: Array<{
      notes: string;
      leafCategoryName: string;
      scopeType: Enums.PatternScopeType;
      peakPeriods: Array<string>;
      troughPeriods: Array<string>;
      scopeLeafCategoryNames: Array<string>;
      scopeDescription: string | null;
      typicalAmount: number | null;
      amountVariance: number | null;
      peakMultiplier: number | null;
      cyclesObserved: number | null;
    }>;
    recurringPatterns: Array<{
      notes: string;
      amount: number;
      leafCategoryName: string;
      frequency: Enums.RecurringFrequency;
      scopeType: Enums.PatternScopeType;
      scopeLeafCategoryNames: Array<string>;
      dayVariance: number | null;
      lastObserved: string | null;
      firstObserved: string | null;
      amountVariance: number | null;
      occurrenceCount: number | null;
      scopeDescription: string | null;
      typicalDayOfMonth: number | null;
    }>;
  }>;
};

export type DateTransaction = Pick<Transaction, 'date' | 'amount'>;
