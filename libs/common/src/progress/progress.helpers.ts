import { redisService } from '../services';

const invoiceBill = 'invoice-bill-categorization-progress:';
const transaction = 'transaction-categorization-progress:';
const forecast = 'forecast-progress:';

function setInvoiceBillCategorizationProgress(
  companyId: number,
  percentageProgress: string,
  countProgress = '',
  timeEstimate = '',
) {
  const progressObject = JSON.stringify({ percentageProgress, countProgress, timeEstimate });
  return redisService.SET(`${invoiceBill}${companyId}`, progressObject);
}

function getInvoiceBillCategorizationProgress(companyId: number) {
  return redisService.GET(`${invoiceBill}${companyId}`);
}

function setTransactionCategorizationProgress(
  companyId: number,
  percentageProgress: string,
  countProgress = '',
  timeEstimate = '',
  runInBackground = false,
) {
  const progressObject = JSON.stringify({ percentageProgress, countProgress, timeEstimate, runInBackground });
  return redisService.SET(`${transaction}${companyId}`, progressObject);
}

function getTransactionCategorizationProgress(companyId: number) {
  return redisService.GET(`${transaction}${companyId}`);
}

function setForecastProgress(companyId: number, status: string, countProgress = '') {
  return redisService.SET(`${forecast}${companyId}`, JSON.stringify({ status, countProgress }));
}

function getForecastProgress(companyId: number) {
  return redisService.GET(`${forecast}${companyId}`);
}

export default {
  transactionCategorization: { set: setTransactionCategorizationProgress, get: getTransactionCategorizationProgress },
  invoiceBillCategorization: { set: setInvoiceBillCategorizationProgress, get: getInvoiceBillCategorizationProgress },
  forecasting: { set: setForecastProgress, get: getForecastProgress },
};
