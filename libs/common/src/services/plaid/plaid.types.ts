import { Enums } from '@compass/database';

export type CompanyByIdForPlaidFlow = {
  id: number;
  userId: number;
  name: string;
  currency: Enums.Currency;
  country: { name: string; code: string };
  industry: { name: string };
  isFirstCategorizationDone: boolean;
  appFeaturesInProcessing: Array<Enums.AppUsageFeature>;
  accessToken: string;
  nextCursor: string | null;
  plaidAccounts: Array<{
    id: number;
    plaidId: string;
    bankAccountId: number;
  }>;
  remainingUsage: number;
};
