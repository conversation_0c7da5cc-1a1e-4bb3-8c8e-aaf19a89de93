import constants from './entities-processor.bullmq.constants';
import type { ForecastMessage, InvoiceBillMessage, TransactionMessage } from './entities-processor.bullmq.validators';

export type * from './entities-processor.bullmq.validators';

export type QueueDataMap = {
  [constants.jobs.forecast]: ForecastMessage;
  [constants.jobs.transactionCategorization]: TransactionMessage;
  [constants.jobs.invoiceBillCategorization]: InvoiceBillMessage;
};

export type QueueData = QueueDataMap[keyof QueueDataMap];
