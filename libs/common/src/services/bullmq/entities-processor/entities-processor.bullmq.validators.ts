import { Enums } from '@compass/database';
import z from 'zod';

import { categorizationConstants, forecastConstants } from '../../../constants';
import constants from './entities-processor.bullmq.constants';

const forecastJobValidator = z.object({
  type: z.literal(forecastConstants.forecastJobType.initialForecast),
  contextId: z.number(),
  forecastId: z.number(),
});
export type ForecastJob = z.infer<typeof forecastJobValidator>;

const rerunForecastJobValidator = z.object({
  type: z.literal(forecastConstants.forecastJobType.rerunForecast),
  contextId: z.number(),
  forecastId: z.number(),
});
export type RerunForecastJob = z.infer<typeof rerunForecastJobValidator>;

const contextUpdateJobValidator = z.object({
  type: z.literal(forecastConstants.forecastJobType.contextUpdate),
  contextId: z.number(),
  period: z.string(),
});
export type ContextUpdateJob = z.infer<typeof contextUpdateJobValidator>;

const forecastMessageValidator = z.object({
  jobType: z.literal(constants.jobs.forecast),
  companyId: z.number(),
  data: z.union([forecastJobValidator, contextUpdateJobValidator, rerunForecastJobValidator]),
});
export type ForecastMessage = z.infer<typeof forecastMessageValidator>;

const saltEdgeConnectionJobValidator = z.object({
  type: z.literal(categorizationConstants.transactionCategorizationType.saltEdgeConnection),
  connectionId: z.string(),
});
export type SaltEdgeConnectionJob = z.infer<typeof saltEdgeConnectionJobValidator>;

const saltEdgeDailyJobValidator = z.object({
  type: z.literal(categorizationConstants.transactionCategorizationType.saltEdgeDaily),
  accounts: z.array(
    z.object({
      id: z.number(),
      saltEdgeId: z.string(),
      bankAccountId: z.number(),
      connectionId: z.string(),
      lastTransactionId: z.string().nullable(),
    }),
  ),
});
export type SaltEdgeDailyJob = z.infer<typeof saltEdgeDailyJobValidator>;

const plaidConnectionJobValidator = z.object({
  type: z.literal(categorizationConstants.transactionCategorizationType.plaidConnection),
  plaidId: z.string(),
});
export type PlaidConnectionJob = z.infer<typeof plaidConnectionJobValidator>;

const plaidDailyJobValidator = z.object({
  type: z.literal(categorizationConstants.transactionCategorizationType.plaidDaily),
  plaidId: z.string(),
});
export type PlaidDailyJob = z.infer<typeof plaidDailyJobValidator>;

const onboardingFileJobValidator = z.object({
  type: z.literal(categorizationConstants.transactionCategorizationType.onboardingFile),
  bankAccountId: z.number(),
  file: z.object({
    id: z.number(),
    name: z.string(),
    statementFormat: z.nativeEnum(Enums.FileStatementFormat).nullable(),
    key: z.string(),
    version: z.string(),
  }),
});
export type OnboardingFileJob = z.infer<typeof onboardingFileJobValidator>;

const settingsNewFilesJobValidator = z.object({
  type: z.literal(categorizationConstants.transactionCategorizationType.settingsNewFiles),
  bankAccountId: z.number(),
  fileIds: z.array(z.number()).min(1),
});
export type SettingsNewFilesJob = z.infer<typeof settingsNewFilesJobValidator>;

const transactionMessageValidator = z.object({
  jobType: z.literal(constants.jobs.transactionCategorization),
  companyId: z.number(),
  data: z.union([
    saltEdgeConnectionJobValidator,
    saltEdgeDailyJobValidator,
    plaidConnectionJobValidator,
    plaidDailyJobValidator,
    onboardingFileJobValidator,
    settingsNewFilesJobValidator,
  ]),
});
export type TransactionMessage = z.infer<typeof transactionMessageValidator>;

const quickBooksJobValidator = z.object({
  type: z.literal(categorizationConstants.invoiceBillCategorizationType.quickBooks),
  connectionId: z.number(),
});
export type QuickBooksJob = z.infer<typeof quickBooksJobValidator>;

const quickBooksDailyJobValidator = z.object({
  type: z.literal(categorizationConstants.invoiceBillCategorizationType.quickBooksDaily),
  connectionId: z.number(),
});
export type QuickBooksDailyJob = z.infer<typeof quickBooksDailyJobValidator>;

const invoicesBillsFilesJobValidator = z.object({
  type: z.literal(categorizationConstants.invoiceBillCategorizationType.files),
  fileIds: z.number().array().min(1),
});
export type InvoicesBillsFilesJob = z.infer<typeof invoicesBillsFilesJobValidator>;

const invoiceBillMessageValidator = z.object({
  jobType: z.literal(constants.jobs.invoiceBillCategorization),
  companyId: z.number(),
  data: z.union([quickBooksJobValidator, quickBooksDailyJobValidator, invoicesBillsFilesJobValidator]),
  shouldSendFinishedEmail: z.boolean(),
});
export type InvoiceBillMessage = z.infer<typeof invoiceBillMessageValidator>;

const messageValidator = z.union([forecastMessageValidator, transactionMessageValidator, invoiceBillMessageValidator]);

export default { messageValidator };
