import { type JobsOptions, Queue } from 'bullmq';

import logger from '../../../logger';
import constants from './entities-processor.bullmq.constants';
import type { QueueDataMap } from './entities-processor.bullmq.types';

const isLocal = process.env.ENVIRONMENT === 'local';

let queue: Queue | undefined;

if (constants.queueData.url) {
  queue = new Queue(constants.queueData.name, {
    connection: {
      enableOfflineQueue: false,
      url: constants.queueData.url,
      connectTimeout: 30000,
    },
    defaultJobOptions: {
      attempts: constants.queueData.attempts,
      backoff: { type: 'exponential', delay: 500 },
      removeOnComplete: isLocal ? 5 : 20,
      removeOnFail: isLocal ? 5 : 50,
    },
  });

  queue.on('error', (error: unknown) => logger.error(error, 'Queue error'));
} else {
  logger.warn('Queue url not found');
}

async function add<K extends keyof QueueDataMap>(name: K, data: QueueDataMap[K], opts: JobsOptions) {
  return queue?.add(name, data, opts);
}

export default { add };
