import { Enums } from '@compass/database';

export type CompanyByIdForSaltEdgeFlow = {
  id: number;
  userId: number;
  name: string;
  currency: Enums.Currency;
  country: { name: string; code: string };
  industry: { name: string };
  isFirstCategorizationDone: boolean;
  appFeaturesInProcessing: Array<Enums.AppUsageFeature>;
  saltEdgeAccounts: Array<{
    id: number;
    saltEdgeId: string;
    bankAccountId: number;
    lastTransactionId: string | null;
  }>;
  remainingUsage: number;
};

export interface SaltEdgeResponse<T> {
  data: T;
}

export interface SaltEdgeResponseWithMeta<T> {
  data: T;
  meta: { next_id: string | null; next_page: string | null };
}

export interface SaltEdgeLead {
  email: string;
  customer_id: string;
  identifier: string;
}

export interface SaltEdgeLeadSession {
  expires_at: Date;
  redirect_url: string;
}

type SaltEdgeScopes =
  | ['account_details']
  | ['holder_information']
  | ['account_details', 'holder_information']
  | ['account_details', 'transactions_details']
  | ['account_details', 'holder_information', 'transactions_details'];

export type SaltEdgePartnerConsent = {
  id: string;
  connection_id: string;
  customer_id: string;
  status: 'active' | 'revoked';
  revoked_by: 'partner' | 'lead' | 'saltedge';
  revoked_at: Date;
  created_at: Date;
  updated_at: Date;
};

export type SaltEdgeConsent = {
  id: string;
  connection_id: string;
  customer_id: string;
  scopes: SaltEdgeScopes;
  status: 'active' | 'expired' | 'revoked';
  revoked_by: 'partner' | 'lead' | 'saltedge';
  period_days: number;
  expires_at: Date;
  from_date: Date;
  revoked_at: Date;
  created_at: Date;
  updated_at: Date;
};

export interface SaltEdgeAccount {
  id: string;
  connection_id: string;
  name: string;
  nature:
    | 'account'
    | 'bonus'
    | 'card'
    | 'checking'
    | 'credit'
    | 'credit_card'
    | 'debit_card'
    | 'ewallet'
    | 'insurance'
    | 'investment'
    | 'loan'
    | 'mortgage'
    | 'savings';
  balance: number;
  currency_code: Enums.Currency;
  extra: {
    bban?: string;
    iban?: string;
    sort_code?: string;
    account_name?: string;
    client_name?: string;
    transactions_count?: { posted: number; pending: number };
    last_posted_transaction_id?: string;
  };
  created_at: Date;
  updated_at: Date;
}

export interface SaltEdgeTransaction {
  id: string;
  account_id: string;
  made_on: string;
  amount: number;
  currency_code: Enums.Currency;
  description: string;
  category: string;
  extra: {
    id?: string;
    payee?: string;
    payee_information?: string;
    payer?: string;
    payer_information?: string;
    merchant_id?: string;
    time?: string;
  };
}

export type SaltEdgeTransactionWithDBAccounts = SaltEdgeTransaction & {
  bankAccountId: number;
  saltEdgeAccountId: number;
};

export interface SaltEdgeMerchant {
  id: string;
  names: {
    mode: 'name' | 'transliterated_name' | 'alternative_name' | 'brand' | 'operator';
    value: string;
  }[];
}

export interface SaltEdgeSessionData {
  customer_id: string | null;
  connection_id: string | null;
  consent: { scopes: SaltEdgeScopes; from_date: string };
  attempt: { return_to: string };
  categorization: 'none' | 'personal' | 'business';
  return_error_class: boolean;
}

export interface SaltEdgeConnection {
  id: string;
  provider_id: string;
  provider_name: string;
  customer_id: string;
  status: 'active' | 'inactive' | 'disabled';
}

export type SaltEdgeCustomer = {
  id: string;
  identifier: string;
  secret: string;
  updated_at: Date;
  created_at: Date;
  blocked_at: Date | null;
};

export type SaltEdgeDeletedCustomer = { deleted: boolean; lead_email: string };
