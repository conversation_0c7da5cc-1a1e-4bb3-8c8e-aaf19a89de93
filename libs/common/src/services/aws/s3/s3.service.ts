import {
  DeleteObjectCommand,
  type DeleteObjectCommandOutput,
  GetObjectCommand,
  PutObjectCommand,
  type PutObjectCommandOutput,
  S3Client,
} from '@aws-sdk/client-s3';
import type { StreamingBlobPayloadInputTypes, StreamingBlobPayloadOutputTypes } from '@smithy/types';

const client = new S3Client({ region: process.env.AWS_REGION });

async function getObject(
  Bucket: string,
  Key: string,
  VersionId?: string,
): Promise<StreamingBlobPayloadOutputTypes | undefined> {
  const response = await client.send(new GetObjectCommand({ Bucket, Key, VersionId }));
  return response.Body;
}

async function getObjectAsString(...args: Parameters<typeof getObject>): Promise<string | undefined> {
  const body = await getObject(...args);
  return body?.transformToString();
}

async function getObjectAsJson(...args: Parameters<typeof getObject>): Promise<unknown | undefined> {
  const body = await getObjectAsString(...args);
  return body ? JSON.parse(body) : undefined;
}

function uploadObject(
  Bucket: string,
  Key: string,
  Body: StreamingBlobPayloadInputTypes,
  ContentType?: string,
): Promise<PutObjectCommandOutput> {
  return client.send(new PutObjectCommand({ Bucket, Key, Body, ContentType }));
}

function deleteObject(Bucket: string, Key: string, VersionId: string): Promise<DeleteObjectCommandOutput> {
  return client.send(new DeleteObjectCommand({ Bucket, Key, VersionId }));
}

export default { getObject, getObjectAsString, getObjectAsJson, uploadObject, deleteObject };
