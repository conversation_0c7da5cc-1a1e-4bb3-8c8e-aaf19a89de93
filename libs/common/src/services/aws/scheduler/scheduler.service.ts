import {
  CreateScheduleCommand,
  CreateScheduleCommandInput,
  DeleteScheduleCommand,
  GetScheduleCommand,
  GetScheduleCommandInput,
  ResourceNotFoundException,
  SchedulerClient,
} from '@aws-sdk/client-scheduler';

import logger from '../../../logger';

const schedulerClient = new SchedulerClient({ region: process.env.AWS_REGION });

async function checkIfScheduleExists(data: GetScheduleCommandInput) {
  try {
    await schedulerClient.send(new GetScheduleCommand(data));
    return true;
  } catch (error) {
    if (error instanceof ResourceNotFoundException) {
      return false;
    }

    throw error;
  }
}

async function createSchedule(data: CreateScheduleCommandInput) {
  const scheduleExists = await checkIfScheduleExists({ Name: data.Name, GroupName: data.GroupName });
  if (scheduleExists) return;

  return schedulerClient.send(new CreateScheduleCommand(data));
}

async function deleteSchedule(Name: string, GroupName: string) {
  try {
    return await schedulerClient.send(new DeleteScheduleCommand({ Name, GroupName }));
  } catch (error) {
    if (error instanceof ResourceNotFoundException) {
      logger.error(`Schedule with name ${Name} not found`);
      return;
    }

    throw error;
  }
}

export default { createSchedule, deleteSchedule };
