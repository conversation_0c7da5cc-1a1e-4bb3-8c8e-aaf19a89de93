import { SendEmailCommand, SendEmailCommandInput, SESClient } from '@aws-sdk/client-ses';

import { configConstants } from '../../../constants';
import logger from '../../../logger';

const sesClient = new SESClient({ region: process.env.AWS_REGION });

const envMapper = { local: '(LOCAL) ', development: '(DEV) ', staging: '(STAGING) ', production: '' } as const;
const env = envMapper[process.env.ENVIRONMENT as keyof typeof envMapper] || '';

async function sendEmail({
  toAddresses,
  subject,
  body,
  bodyType = 'html',
  source,
}: {
  toAddresses: Array<string>;
  subject: string;
  body: string;
  bodyType?: 'html' | 'text';
  source?: string;
}) {
  if (process.env.SEND_EMAIL === 'false') return logger.warn('Email sending disabled.');

  const data = { Data: body };

  const params: SendEmailCommandInput = {
    Destination: { ToAddresses: toAddresses },
    Message: {
      Subject: { Data: `${env}${subject}` },
      Body: bodyType === 'html' ? { Html: data } : { Text: data },
    },
    Source: source ?? configConstants.aws.sourceEmail,
  };

  return sesClient.send(new SendEmailCommand(params));
}

export default { sendEmail };
