function generatePromptSuggestionsScheduleName(companyId: number) {
  return `prompt-suggestions-${companyId}`;
}

function generateWeeklyFinancialInsightsScheduleName(companyId: number) {
  return `financial-insights-weekly-${companyId}`;
}

function generateMonthlyFinancialInsightsScheduleName(companyId: number) {
  return `financial-insights-monthly-${companyId}`;
}

function generateMonthlyWidgetInsightsScheduleName(companyId: number) {
  return `widget-insights-monthly-${companyId}`;
}

function generateMonitoringScheduleName(companyId: number) {
  return `monitoring-${companyId}`;
}

function generateQuickBooksDailyScheduleName(companyId: number) {
  return `quickBooks-daily-${companyId}`;
}

function generatePlaidTransactionsDailyScheduleName(companyId: number) {
  return `plaid-transactions-daily-${companyId}`;
}

function generateSaltEdgeTransactionsDailyScheduleName(companyId: number) {
  return `saltEdge-transactions-daily-${companyId}`;
}

export default {
  generatePromptSuggestionsScheduleName,
  generateWeeklyFinancialInsightsScheduleName,
  generateMonthlyFinancialInsightsScheduleName,
  generateMonthlyWidgetInsightsScheduleName,
  generateMonitoringScheduleName,
  generateQuickBooksDailyScheduleName,
  generatePlaidTransactionsDailyScheduleName,
  generateSaltEdgeTransactionsDailyScheduleName,
};
