import { X2jOptions, XMLParser } from 'fast-xml-parser';

import { BadRequestError } from '../helpers';

const defaultOptions = { ignoreAttributes: false, attributeNamePrefix: '__' };

export default async function parseXml(buffer: Buffer | string, options?: X2jOptions) {
  const json = new XMLParser({ ...defaultOptions, ...options }).parse(buffer);

  if (typeof json !== 'object') throw new BadRequestError('Invalid file.');

  return json;
}
