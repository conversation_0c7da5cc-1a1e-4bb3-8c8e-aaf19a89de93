import _ from 'lodash';

import type { CustomHttpErrorCode } from '../types';

class BaseError extends Error {
  status: number;
  payload: unknown;
  internalPayload: unknown;
  code?: CustomHttpErrorCode;

  constructor(
    status: number,
    message: string,
    payload?: unknown,
    internalPayload?: unknown,
    code?: CustomHttpErrorCode,
  ) {
    super();
    this.status = status;
    this.payload = payload;
    this.internalPayload = internalPayload;
    this.code = code;
    this.message = message.endsWith('.') ? message : `${message}.`;
    this.name = this.constructor.name;
    Error.captureStackTrace(this, this.constructor);
  }
}

class BadRequestError extends BaseError {
  constructor(message = 'Bad request', payload?: unknown, internalPayload?: unknown, code?: CustomHttpErrorCode) {
    super(400, message, payload, internalPayload, code);
  }
}

class ValidationError extends BaseError {
  constructor(message = 'Validation error', payload?: unknown, internalPayload?: unknown, code?: CustomHttpErrorCode) {
    super(400, message, payload, internalPayload, code);
  }
}

class UnauthorizedError extends BaseError {
  constructor(message = 'Unauthorized', payload?: unknown, internalPayload?: unknown, code?: CustomHttpErrorCode) {
    super(401, message, payload, internalPayload, code);
  }
}

class ForbiddenError extends BaseError {
  constructor(message = 'Forbidden', payload?: unknown, internalPayload?: unknown, code?: CustomHttpErrorCode) {
    super(403, message, payload, internalPayload, code);
  }
}

class NotFoundError extends BaseError {
  constructor(entity: string, payload?: unknown, internalPayload?: unknown, code?: CustomHttpErrorCode) {
    super(404, `${_.capitalize(entity)} not found.`, payload, internalPayload, code);
  }
}

class ConflictError extends BaseError {
  constructor(message = 'Conflict', payload?: unknown, internalPayload?: unknown, code?: CustomHttpErrorCode) {
    super(409, message, payload, internalPayload, code);
  }
}

class UnprocessableEntityError extends BaseError {
  constructor(
    message = 'Unprocessable Entity',
    payload?: unknown,
    internalPayload?: unknown,
    code?: CustomHttpErrorCode,
  ) {
    super(422, message, payload, internalPayload, code);
  }
}

class InternalServerError extends BaseError {
  constructor(message = 'Internal Server Error', internalPayload?: unknown, code?: CustomHttpErrorCode) {
    super(500, message, undefined, internalPayload, code);
  }
}

class NotImplementedError extends BaseError {
  constructor(message = 'Not Implemented Error', internalPayload?: unknown, code?: CustomHttpErrorCode) {
    super(501, message, undefined, internalPayload, code);
  }
}

export {
  BadRequestError,
  ConflictError,
  ForbiddenError,
  InternalServerError,
  NotFoundError,
  NotImplementedError,
  UnauthorizedError,
  UnprocessableEntityError,
  ValidationError,
};
