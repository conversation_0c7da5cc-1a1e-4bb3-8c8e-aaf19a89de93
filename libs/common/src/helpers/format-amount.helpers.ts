import { Enums } from '@compass/database';

function formatAmountWithCurrency(value: number, currency: Enums.Currency) {
  const locale = currency === Enums.Currency.USD ? 'en-US' : 'de-DE';
  const currencyDisplay =
    currency === Enums.Currency.AUD || currency === Enums.Currency.CAD ? 'symbol' : 'narrowSymbol';

  return Intl.NumberFormat(locale, { style: 'currency', currency, currencyDisplay }).format(value);
}

export default { formatAmountWithCurrency };
