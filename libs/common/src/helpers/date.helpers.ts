import type { Month, YearMonth } from '@compass/common/types';
import dayjs from 'dayjs';

import { dateConstants } from '../constants';
import { InternalServerError } from '../helpers';

function getYearMonth(date: Date) {
  return dayjs(date).format('YYYY-MM') as YearMonth;
}

function getStartOfPeriod(date: YearMonth | Date): Date {
  if (date instanceof Date) return dayjs(date).startOf('month').toDate();

  return dayjs(`${date}-01`).startOf('month').toDate();
}

function getEndOfPeriod(date: YearMonth | Date): Date {
  if (date instanceof Date) return dayjs(date).endOf('month').toDate();

  return dayjs(`${date}-01`).endOf('month').toDate();
}

function transformDate(dateFormat: string, date: string) {
  const addCenturyIfMissing = (year: number) => (year < 100 ? year + 2000 : year);

  const upperCasedDateFormat = dateFormat.toUpperCase();
  const [part1, part2, part3] = date.split(/[.\/-]/).map(Number);

  if (upperCasedDateFormat === dateConstants.dateFormats.day) {
    return new Date(`${addCenturyIfMissing(part3)}-${part2}-${part1}`);
  }

  if (upperCasedDateFormat === dateConstants.dateFormats.month) {
    return new Date(`${addCenturyIfMissing(part3)}-${part1}-${part2}`);
  }

  if (upperCasedDateFormat === dateConstants.dateFormats.year) {
    return new Date(`${addCenturyIfMissing(part1)}-${part2}-${part3}`);
  }

  throw new InternalServerError('Invalid date format.');
}

function getPreviousPeriod(period: YearMonth): YearMonth {
  return dayjs(`${period}-01`).subtract(1, 'month').format('YYYY-MM') as YearMonth;
}

function extractValuesFromPeriod(period: YearMonth) {
  const [yearString, monthString] = period.split('-');

  return { year: Number(yearString), month: Number(monthString) };
}

function convertMonthsToPeriods(months: Array<string>, baseYear: number): Array<YearMonth> {
  return months.map((month) => `${baseYear}-${month.padStart(2, '0')}` as YearMonth);
}

function convertPeriodsToMonths(periods: Array<string>): Array<Month> {
  return periods.map((p) => p.split('-')[1] || '01') as Array<Month>;
}

/** Generate an array of periods between the first transaction and end period, inclusive. */
function generatePeriods(startPeriod: YearMonth | undefined, endPeriod: YearMonth): Array<YearMonth> {
  if (!startPeriod) return [];

  const end = dayjs(getEndOfPeriod(endPeriod));
  let current = dayjs(getStartOfPeriod(startPeriod));

  const periods = new Set<YearMonth>();

  while (current.isBefore(end) || current.isSame(end, 'month')) {
    periods.add(getYearMonth(current.toDate()));
    current = current.add(1, 'month');
  }

  return [...periods];
}

export default {
  getYearMonth,
  getStartOfPeriod,
  getEndOfPeriod,
  transformDate,
  getPreviousPeriod,
  extractValuesFromPeriod,
  convertMonthsToPeriods,
  convertPeriodsToMonths,
  generatePeriods,
};
