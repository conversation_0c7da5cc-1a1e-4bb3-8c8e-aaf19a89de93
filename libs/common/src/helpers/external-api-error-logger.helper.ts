import { isAxiosError } from 'axios';
import _ from 'lodash';

import logger from '../logger';

function logExternalApiError(message: string, error: unknown, data?: object): void {
  const dataWithoutSensitiveInfo = _.omit(data, ['password', 'user.password']);

  if (!isAxiosError(error)) return logger.error({ error, data: dataWithoutSensitiveInfo }, message);

  if (!error.response) return logger.error({ error, data: dataWithoutSensitiveInfo }, message);

  logger.error(
    {
      requestData: error.response.config.data,
      status: error.response.status,
      responseData: error.response.data,
      data: dataWithoutSensitiveInfo,
    },
    message,
  );
}

export default logExternalApiError;
