import { Readable } from 'stream';

async function streamToBuffer(readable: Readable): Promise<Buffer> {
  const chunks: Array<any> = [];

  for await (const data of readable) {
    if (typeof data === 'string') chunks.push(Buffer.from(data, 'utf-8'));
    else if (data instanceof Buffer) chunks.push(data);
    else chunks.push(Buffer.from(JSON.stringify(data), 'utf-8'));
  }

  return Buffer.concat(chunks);
}

export default { streamToBuffer };
