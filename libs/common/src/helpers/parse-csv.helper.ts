import { Options, parse } from 'csv-parse';
import { Readable } from 'stream';

const defaultOptions: Options = { trim: true, columns: true, relaxColumnCountLess: true, delimiter: ';' };

export default async function parseCsv(buffer: Buffer, options?: Options) {
  const records: any[] = [];
  const parser = Readable.from(buffer).pipe(parse({ ...defaultOptions, ...options }));

  for await (const record of parser) {
    if (Object.values(record).some((value) => value != null && value !== '')) {
      records.push(record);
    }
  }

  return records;
}
