import { Enums } from '@compass/database';

const restrictMutationsErrorMessage = 'Action not available. Your account is in readonly mode.' as const;

const restrictFeatureErrorMessage = 'Feature not available.' as const;

const restrictFeatureUsageErrorMessage = 'Feature limit reached.' as const;

const restrictProcessingErrorMessages = {
  [Enums.AppUsageFeature.INVOICES_BILLS]: 'Invoices/Bills processing in progress. Try again later.',
  [Enums.AppUsageFeature.TRANSACTIONS]: 'Transactions processing in progress. Try again later.',
  [Enums.AppUsageFeature.FORECAST]: 'Forecast processing in progress. Try again later.',
} as const;

const forecastBlockingProcessInProgressErrorMessages = {
  [Enums.AppUsageFeature.TRANSACTIONS]: 'Forecasting can be run after transactions categorization is completed.',
  [Enums.AppUsageFeature.INVOICES_BILLS]:
    'Forecasting can be run after invoices and bills categorization is completed.',
} as const;

const categorizationBlockingProcessInProgressErrorMessages = {
  [Enums.AppUsageFeature.FORECAST]: 'Categorization can be run after forecasting is completed.',
} as const;

const restrictProcessingFallbackErrorMessage = 'Feature processing in progress. Try again later.' as const;

export default {
  restrictMutationsErrorMessage,
  restrictFeatureErrorMessage,
  restrictFeatureUsageErrorMessage,
  restrictProcessingErrorMessages,
  forecastBlockingProcessInProgressErrorMessages,
  categorizationBlockingProcessInProgressErrorMessages,
  restrictProcessingFallbackErrorMessage,
};
