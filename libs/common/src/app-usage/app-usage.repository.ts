import { db, Enums } from '@compass/database';

import type { Transaction } from '../types';

function getAppUsage(companyId: number, feature: Enums.AppUsageFeature) {
  return db.appUsage.findUnique({
    where: { companyId_feature: { companyId, feature } },
    select: { max: true, usage: true, maxOverride: true },
  });
}

function setFeatureInProcessing(companyId: number, feature: Enums.AppUsageFeature, tx: Transaction) {
  return tx.$queryRaw<Array<{ success: true }>>`
    UPDATE "Company"
    SET "appFeaturesInProcessing" = ARRAY_APPEND(COALESCE("appFeaturesInProcessing", '{}')::"AppUsageFeature"[], ${feature}::"AppUsageFeature")
    WHERE 
      id = ${companyId}
      AND NOT (${feature}::"AppUsageFeature" = ANY(COALESCE("appFeaturesInProcessing", '{}')::"AppUsageFeature"[]))
    RETURNING TRUE AS success
  `;
}

function unsetFeatureInProcessing(companyId: number, feature: Enums.AppUsageFeature, tx?: Transaction) {
  const database = tx ?? db;

  return database.$queryRaw`
    UPDATE "Company"
    SET "appFeaturesInProcessing" = ARRAY_REMOVE(COALESCE("appFeaturesInProcessing", '{}')::"AppUsageFeature"[], ${feature}::"AppUsageFeature")
    WHERE id = ${companyId}
  `;
}

export default { getAppUsage, setFeatureInProcessing, unsetFeatureInProcessing };
