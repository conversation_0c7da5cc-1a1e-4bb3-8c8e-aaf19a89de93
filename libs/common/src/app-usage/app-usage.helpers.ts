import { Enums } from '@compass/database';

import { httpErrorsConstants } from '../constants';
import { ForbiddenError } from '../helpers';
import type { Transaction } from '../types';
import constants from './app-usage.constants';
import repository from './app-usage.repository';

const { customErrorCode } = httpErrorsConstants;
const {
  restrictFeatureUsageErrorMessage,
  restrictProcessingErrorMessages,
  forecastBlockingProcessInProgressErrorMessages,
  categorizationBlockingProcessInProgressErrorMessages,
  restrictProcessingFallbackErrorMessage,
} = constants;

function getLimit(max: number | null, maxOverride: number | null) {
  if (max === null && maxOverride === null) return null;

  if (maxOverride !== null && (max === null || maxOverride > max)) return maxOverride;

  return max;
}

function getRemainingUsage({
  max,
  usage,
  maxOverride,
}: {
  max: number | null;
  usage: number;
  maxOverride: number | null;
}) {
  const limit = getLimit(max, maxOverride);

  return limit !== null ? limit - usage : Infinity;
}

function getCheckEntitiesLengthErrorMessage(feature: Enums.AppUsageFeature, remainingUsage: number) {
  switch (feature) {
    case Enums.AppUsageFeature.TRANSACTIONS:
      return `Maximum number of transactions is ${remainingUsage}.`;
    case Enums.AppUsageFeature.INVOICES_BILLS:
      return `Maximum number of invoices/bills is ${remainingUsage}.`;
    default:
      return restrictFeatureUsageErrorMessage;
  }
}

async function checkEntitiesLength(companyId: number, feature: Enums.AppUsageFeature, entitiesLength: number) {
  const appUsage = await repository.getAppUsage(companyId, feature);

  if (!appUsage) {
    throw new ForbiddenError(restrictFeatureUsageErrorMessage, { feature }, undefined, customErrorCode.appUsage);
  }

  const remainingUsage = getRemainingUsage(appUsage);

  if (remainingUsage < entitiesLength) {
    throw new ForbiddenError(
      getCheckEntitiesLengthErrorMessage(feature, remainingUsage),
      { feature },
      undefined,
      customErrorCode.appUsage,
    );
  }
}

function getRestrictProcessingErrorMessage(feature: Enums.AppUsageFeature) {
  switch (feature) {
    case Enums.AppUsageFeature.TRANSACTIONS:
    case Enums.AppUsageFeature.INVOICES_BILLS:
    case Enums.AppUsageFeature.FORECAST:
      return restrictProcessingErrorMessages[feature];
    default:
      return restrictProcessingFallbackErrorMessage;
  }
}

async function setFeatureInProcessing(companyId: number, feature: Enums.AppUsageFeature, tx: Transaction) {
  const response = await repository.setFeatureInProcessing(companyId, feature, tx);

  if (response.length === 0) {
    throw new ForbiddenError(
      getRestrictProcessingErrorMessage(feature),
      { feature },
      undefined,
      customErrorCode.appUsage,
    );
  }
}

function unsetFeatureInProcessing(companyId: number, feature: Enums.AppUsageFeature, tx?: Transaction) {
  return repository.unsetFeatureInProcessing(companyId, feature, tx);
}

function isCategorizationBlockingProcessInProgress(
  appFeaturesInProcessing: Array<Enums.AppUsageFeature>,
): string | void {
  const categorizationBlockingFeatures = [Enums.AppUsageFeature.FORECAST];

  for (const feature of categorizationBlockingFeatures) {
    if (appFeaturesInProcessing.includes(feature)) {
      return categorizationBlockingProcessInProgressErrorMessages[feature];
    }
  }
}

function isForecastBlockingProcessInProgress(appFeaturesInProcessing: Array<Enums.AppUsageFeature>): string | void {
  const forecastBlockingFeatures = [Enums.AppUsageFeature.TRANSACTIONS, Enums.AppUsageFeature.INVOICES_BILLS];

  for (const feature of forecastBlockingFeatures) {
    if (appFeaturesInProcessing.includes(feature)) {
      return forecastBlockingProcessInProgressErrorMessages[feature];
    }
  }
}

export default {
  getLimit,
  getRemainingUsage,
  checkEntitiesLength,
  getRestrictProcessingErrorMessage,
  setFeatureInProcessing,
  unsetFeatureInProcessing,
  isCategorizationBlockingProcessInProgress,
  isForecastBlockingProcessInProgress,
};
