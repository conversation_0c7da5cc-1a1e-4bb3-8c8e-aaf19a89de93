import { Enums } from '@compass/database';

import { ConflictError } from '../helpers';
import repository from './leaf-category.repository';

async function validateLeafCategory(
  companyId: number,
  leafCategoryId: number | null | undefined,
  type: Enums.FinancialType,
) {
  if (!leafCategoryId) return;

  const companyLeafCategory = await repository.getCompanyLeafCategory(companyId, leafCategoryId);

  if (!companyLeafCategory) throw new ConflictError('Leaf category not found or inactive.', { leafCategoryId });

  if (companyLeafCategory.type !== type) {
    throw new ConflictError('Leaf category is not of the same type.', { leafCategoryId });
  }
}

export default { validateLeafCategory };
