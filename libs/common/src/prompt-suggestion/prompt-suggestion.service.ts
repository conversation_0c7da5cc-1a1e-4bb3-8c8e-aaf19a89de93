import { Enums } from '@compass/database';

import { NotImplementedError } from '../helpers';
import helpers from './prompt-suggestion.helpers';

function handlePromptSuggestionsCreation(companyId: number, type: Enums.PromptSuggestionType) {
  switch (type) {
    case Enums.PromptSuggestionType.WIDGET:
      return helpers.handleWidgetPromptSuggestionsCreation(companyId);
    case Enums.PromptSuggestionType.AI_ASSISTANT:
      return helpers.handleAiAssistantPromptSuggestionsCreation(companyId);
    default:
      throw new NotImplementedError(`Prompt suggestions type ${type} is not supported`);
  }
}

function handlePromptSuggestionsScheduleCreation(companyId: number) {
  return helpers.createPromptSuggestionsSchedule(companyId);
}

export default { handlePromptSuggestionsCreation, handlePromptSuggestionsScheduleCreation };
