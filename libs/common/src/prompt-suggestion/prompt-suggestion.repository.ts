import { db, Enums } from '@compass/database';
import { Prisma } from '@prisma/client';
import dayjs from 'dayjs';
import _ from 'lodash';

import { ConflictError } from '../helpers';

function upsertPromptSuggestions(
  companyId: number,
  type: Enums.PromptSuggestionType,
  data: Prisma.PromptSuggestionCreateManyArgs['data'],
) {
  return db.$transaction([
    db.promptSuggestion.deleteMany({ where: { companyId, type } }),
    db.promptSuggestion.createMany({ data }),
  ]);
}

async function getCompanyForPromptSuggestions(companyId: number) {
  const company = await db.company.findUnique({
    where: { id: companyId },
    select: {
      industry: { select: { name: true } },
      transactions: { select: { date: true }, orderBy: { date: 'asc' }, take: 1 },
      invoices: { select: { issueDate: true }, orderBy: { issueDate: 'asc' }, take: 1 },
      bills: { select: { issueDate: true }, orderBy: { issueDate: 'asc' }, take: 1 },
    },
  });

  if (!company) throw new ConflictError('Company not found.', { companyId });
  if (!company.industry) throw new ConflictError('Company industry not found.', { companyId });
  if (!company.transactions[0] && !company.invoices[0] && !company.bills[0]) {
    throw new ConflictError('Company has no entries.', { companyId });
  }

  const entryDateStart = _.min([
    company.transactions[0]?.date,
    company.invoices[0]?.issueDate,
    company.bills[0]?.issueDate,
  ]);

  return {
    industryName: company.industry.name,
    entryDateStart: dayjs(entryDateStart).format('YYYY-MM-DD'),
    hasTransactions: !!company.transactions[0],
    hasAccrual: !!(company.invoices[0] || company.bills[0]),
  };
}

export default { upsertPromptSuggestions, getCompanyForPromptSuggestions };
