import { Enums } from '@compass/database';
import dayjs from 'dayjs';

import type { CompassAi } from '../../types';
import { configConstants } from '../constants';
import { scheduleNamesHelpers } from '../helpers';
import { awsCommon, compassAiRepository, compassAiService } from '../services';
import repository from './prompt-suggestion.repository';

async function createPromptSuggestionsSchedule(companyId: number) {
  // We don't have EventBridge Schedule local integration yet.
  // If you want prompt suggestions, execute local server in prompt-suggestions service
  if (process.env.ENVIRONMENT !== 'production') return;

  const currentMinutes = dayjs().minute();

  return awsCommon.schedulerService.createSchedule({
    Name: scheduleNamesHelpers.generatePromptSuggestionsScheduleName(companyId),
    GroupName: configConstants.aws.promptSuggestionServiceSchedulerGroup,
    ScheduleExpression: `cron(${currentMinutes} 7 1 * ? *)`, // Every first of month (UTC)
    FlexibleTimeWindow: {
      Mode: 'OFF',
    },
    Target: {
      Arn: configConstants.aws.promptSuggestionServiceServiceLambdaArn,
      RoleArn: configConstants.aws.promptSuggestionServiceSchedulerRoleArn,
      Input: JSON.stringify({ companyId }),
      RetryPolicy: {
        MaximumRetryAttempts: 5,
        MaximumEventAgeInSeconds: 300,
      },
    },
  });
}

async function handleWidgetPromptSuggestionsCreation(companyId: number) {
  const [company, leafCategories] = await Promise.all([
    repository.getCompanyForPromptSuggestions(companyId),
    compassAiRepository.getCompaniesLeafCategoriesForAi(companyId),
  ]);

  const widgetPromptSuggestionsRequestData: CompassAi.WidgetPromptSuggestionsInput = {
    companyId,
    industry: company.industryName,
    entryDateStart: company.entryDateStart,
    hasTransactions: company.hasTransactions,
    hasAccrual: company.hasAccrual,
    leafCategories,
  };

  const widgetPromptSuggestions = await compassAiService.widgetPromptSuggestions(widgetPromptSuggestionsRequestData);

  const type = Enums.PromptSuggestionType.WIDGET;

  const widgetPromptSuggestionsForDb = widgetPromptSuggestions.map((widgetPromptSuggestion) => ({
    prompt: widgetPromptSuggestion,
    type,
    companyId,
  }));

  return repository.upsertPromptSuggestions(companyId, type, widgetPromptSuggestionsForDb);
}

async function handleAiAssistantPromptSuggestionsCreation(companyId: number) {
  const [company, leafCategories] = await Promise.all([
    repository.getCompanyForPromptSuggestions(companyId),
    compassAiRepository.getCompaniesLeafCategoriesForAi(companyId),
  ]);

  const aiAssistantPromptSuggestionsRequestData: CompassAi.AiAssistantPromptSuggestionsInput = {
    companyId,
    entryDateStart: company.entryDateStart,
    hasTransactions: company.hasTransactions,
    hasAccrual: company.hasAccrual,
    leafCategories,
  };

  const aiAssistantPromptSuggestions = await compassAiService.aiAssistantPromptSuggestions(
    aiAssistantPromptSuggestionsRequestData,
  );

  const type = Enums.PromptSuggestionType.AI_ASSISTANT;

  const aiAssistantPromptSuggestionsForDb = aiAssistantPromptSuggestions.map((aiAssistantPromptSuggestion) => ({
    prompt: aiAssistantPromptSuggestion,
    type,
    companyId,
  }));

  return repository.upsertPromptSuggestions(companyId, type, aiAssistantPromptSuggestionsForDb);
}

export default {
  createPromptSuggestionsSchedule,
  handleWidgetPromptSuggestionsCreation,
  handleAiAssistantPromptSuggestionsCreation,
};
