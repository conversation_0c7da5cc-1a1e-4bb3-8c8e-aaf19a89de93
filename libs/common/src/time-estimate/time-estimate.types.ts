import { Enums } from '@compass/database';

export type ImportFlowContextType = {
  readonly processedCount: number;
  readonly totalCount: number;
  readonly currentAverageTime: number;
  readonly times: Array<number>;
  readonly timeEstimate: string;
  incrementProcessedCount: (count: number) => void;
  addTime: (time: number) => void;
  setCurrentAverageTime: (time: number) => void;
  resetTimes: () => void;
  setTimeEstimate: (timeEstimate: string) => void;
};

export type TransactionCategorizationContextType = {
  readonly company: {
    readonly id: number;
    readonly name: string;
    readonly currency: Enums.Currency;
    readonly userId: number;
    readonly country: string;
    readonly countryCode: string;
    readonly industry: string;
    readonly isFirstCategorizationDone: boolean;
    readonly appFeaturesInProcessing: Array<Enums.AppUsageFeature>;
  };
  readonly bankCardNumbers: Set<string>;
  readonly leafCategoryIds: Array<number>;
  readonly clearingCreditCardPaymentsLeafCategoryId: number;
  readonly creditBankAccountIds: Array<number>;
  readonly processedCount: number;
  readonly totalCount: number;
  readonly currentAverageTime: number;
  readonly times: Array<number>;
  readonly timeEstimate: string;
  readonly runInBackground: boolean;
  incrementProcessedCount: (count: number) => void;
  setLeafCategoryIds: (leafCategoryIds: Array<number>) => void;
  addTime: (time: number) => void;
  setCurrentAverageTime: (time: number) => void;
  resetTimes: () => void;
  setTimeEstimate: (timeEstimate: string) => void;
  setRunInBackground: () => void;
};
