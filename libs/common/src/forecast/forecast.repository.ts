import { db } from '@compass/database';
import { Prisma } from '@prisma/client';

function duplicateForecast({
  companyId,
  sourceForecastId,
  isFullCopy,
  name,
  isTracked,
}: {
  companyId: number;
  sourceForecastId: number;
  isFullCopy: boolean;
  name: string;
  isTracked: boolean;
}) {
  return db.$transaction(async (tx) => {
    const [newForecast]: Array<{ id: number }> = await tx.$queryRaw`
      INSERT INTO "Forecast" ("name", "isTracked", "startPeriod", "endPeriod", "isBase", "companyId", "contextId", "createdAt", "updatedAt")
      SELECT ${name}, ${isTracked}, "startPeriod", "endPeriod", false, "companyId", "contextId", NOW(), NOW()
      FROM "Forecast"
      WHERE "id" = ${sourceForecastId} AND "companyId" = ${companyId}
      RETURNING "id";
    `;

    const accountForecastColumns = isFullCopy
      ? Prisma.sql`"majorAssumptions", "keyRisks", "reasoning"`
      : Prisma.sql`null, null, null`;
    const accountForecasts: Array<{ newId: number; oldId: number }> = await tx.$queryRaw`
      WITH "copied" AS (
        SELECT
          "id" AS "oldId",
          "majorAssumptions",
          "keyRisks",
          "reasoning",
          "privateMerchantId",
          "privateMerchantName",
          ROW_NUMBER() OVER (ORDER BY "id") AS "rowNumber"
        FROM "AccountForecast"
        WHERE "forecastId" = ${sourceForecastId}
      ),
      "inserted" AS (
        INSERT INTO "AccountForecast" ("majorAssumptions", "keyRisks", "reasoning", "privateMerchantId", "privateMerchantName", "forecastId", "createdAt", "updatedAt")
        SELECT ${accountForecastColumns}, "privateMerchantId", "privateMerchantName", ${newForecast.id}, NOW(), NOW()
        FROM "copied"
        RETURNING "id" AS "newId"
      ),
      "numberedInserted" AS (
        SELECT "newId", ROW_NUMBER() OVER (ORDER BY "newId") AS "rowNumber" FROM "inserted"
      )
      SELECT "numberedInserted"."newId", "copied"."oldId"
      FROM "numberedInserted"
      JOIN "copied" ON "numberedInserted"."rowNumber" = "copied"."rowNumber";
    `;

    if (accountForecasts.length > 0) {
      const accountMonthlyForecastColumns = isFullCopy ? '"amount", "reasoning", "raw"' : '0, null, null';
      await tx.$queryRawUnsafe(`
        INSERT INTO "AccountMonthlyForecast" ("amount", "reasoning", "raw", "period", "leafCategoryId", "forecastId", "createdAt", "updatedAt")
        SELECT ${accountMonthlyForecastColumns}, "period", "leafCategoryId", "mapping"."newId", NOW(), NOW()
        FROM "AccountMonthlyForecast"
        JOIN (
          VALUES ${accountForecasts.map(({ oldId, newId }) => `(${oldId}, ${newId})`).join(', ')}
        ) AS "mapping"("oldId", "newId") ON "AccountMonthlyForecast"."forecastId" = "mapping"."oldId"
      `);
    }

    return newForecast;
  });
}

export default { duplicateForecast };
