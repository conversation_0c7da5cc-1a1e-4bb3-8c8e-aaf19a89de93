generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "rhel-openssl-3.0.x"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Company {
  id                            Int               @id @default(autoincrement())
  name                          String            @default("")
  address                       String            @default("")
  vatNumber                     String?
  saltEdgeCustomerId            String?
  stripeCustomerId              String?
  onboardingStep                OnboardingStep    @default(USER_INFO)
  decimalPoint                  DecimalPoint      @default(PERIOD)
  dateFormat                    DateFormat        @default(MM_DD_YYYY)
  currency                      Currency          @default(EUR)
  allowMultipleConnections      Boolean           @default(false)
  trialExpiresAt                DateTime
  status                        CompanyUserStatus @default(TRIAL)
  isFirstCategorizationDone     Boolean           @default(false)
  appFeaturesInProcessing       AppUsageFeature[]
  appRestrictedFeatures         AppUsageFeature[]
  appRestrictedFeaturesOverride AppUsageFeature[]

  industry   CompanyIndustry @relation(fields: [industryId], references: [id])
  industryId Int

  country   Country @relation(fields: [countryId], references: [id])
  countryId Int

  type   CompanyType? @relation(fields: [typeId], references: [id])
  typeId Int?

  bankAccounts BankAccount[]

  bankAccountDailyBalances BankAccountDailyBalance[]

  saltEdgeConnections SaltEdgeConnection[]

  user User?

  categories Category[]

  leafCategories LeafCategory[]

  transactions Transaction[]

  widgetRows WidgetRow[]

  files File[]

  alerts Alert[]

  alertPreviews AlertPreview[]

  alertNotifications AlertNotification[]

  financialInsightNotifications FinancialInsightNotification[]

  financialInsights FinancialInsight[]

  genericNotifications GenericNotification[]

  systemNotifications SystemNotification[]

  privateMerchants PrivateMerchant[]

  promptSuggestions PromptSuggestion[]

  labels Label[]

  plaidItems PlaidItem[]

  widgetInsights WidgetInsight[]

  widgetPreviews WidgetPreview[]

  quickBookConnection QuickBookConnection?

  invoices Invoice[]

  bills Bill[]

  unprocessedTransactions UnprocessedTransaction[]

  unprocessedInvoices UnprocessedInvoice[]

  unprocessedBills UnprocessedBill[]

  invoiceLines InvoiceLine[]

  billLines BillLine[]

  context Context?

  forecasts Forecast[]

  aiAssistantConversations AiAssistantConversation[]

  appSubscription AppSubscription?

  appUsages AppUsage[]

  displayFlags CompanyDisplayFlag?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([stripeCustomerId])
  @@index([industryId])
  @@index([countryId])
  @@index([typeId])
}

model CompanyType {
  id   Int    @id @default(autoincrement())
  name String

  country   Country? @relation(fields: [countryId], references: [id])
  countryId Int?

  companies Company[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([countryId])
}

model User {
  id       Int               @id @default(autoincrement())
  email    String
  name     String
  password String?
  status   CompanyUserStatus @default(TRIAL)

  company   Company @relation(fields: [companyId], references: [id], onDelete: Cascade)
  companyId Int

  companyRole   CompanyRole? @relation(fields: [companyRoleId], references: [id])
  companyRoleId Int?

  widgetRows WidgetRow[]

  socialLogins SocialLogin[]

  alerts Alert[]

  alertPreviews AlertPreview[]

  alertNotifications AlertNotification[]

  systemNotifications SystemNotification[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([companyId])
  @@unique([email])
  @@index([companyRoleId])
}

model SocialLogin {
  id         Int                 @id @default(autoincrement())
  provider   SocialLoginProvider
  credential String

  user   User @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId Int

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([userId])
}

model CompanyRole {
  id   Int    @id @default(autoincrement())
  name String

  users User[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model CompanyIndustryGroup {
  id   Int    @id @default(autoincrement())
  name String

  industries CompanyIndustry[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model CompanyIndustry {
  id   Int    @id @default(autoincrement())
  name String

  industryGroup   CompanyIndustryGroup @relation(fields: [industryGroupId], references: [id])
  industryGroupId Int

  companies Company[]

  leafCategoriesOnIndustries LeafCategoriesOnIndustries[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([industryGroupId])
}

model BankAccount {
  id                      Int             @id @default(autoincrement())
  name                    String          @db.Citext
  displayName             String          @db.Citext
  number                  String?
  currency                Currency
  currentBalance          Float?
  currentConvertedBalance Json?
  isDefault               Boolean         @default(false)
  type                    BankAccountType @default(DEPOSITORY)

  company   Company @relation(fields: [companyId], references: [id], onDelete: Cascade)
  companyId Int

  transactions Transaction[]

  dailyBalances BankAccountDailyBalance[]

  saltEdgeAccounts SaltEdgeAccount[]

  plaidAccounts PlaidAccount[]

  files File[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([companyId])
}

model BankAccountDailyBalance {
  id               Int      @id @default(autoincrement())
  date             DateTime @db.Date
  balance          Float
  convertedBalance Json

  company   Company @relation(fields: [companyId], references: [id], onDelete: Cascade)
  companyId Int

  bankAccount   BankAccount @relation(fields: [bankAccountId], references: [id], onDelete: Cascade)
  bankAccountId Int

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([date, bankAccountId])
  @@index([companyId])
  @@index([bankAccountId])
}

model SaltEdgeAccount {
  id                Int      @id @default(autoincrement())
  saltEdgeId        String
  name              String
  currency          Currency
  lastTransactionId String?

  raw Json?

  connection   SaltEdgeConnection @relation(fields: [connectionId], references: [id], onDelete: Cascade)
  connectionId Int

  bankAccount   BankAccount @relation(fields: [bankAccountId], references: [id], onDelete: Cascade)
  bankAccountId Int

  transactions Transaction[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([saltEdgeId])
  @@index([connectionId])
  @@index([bankAccountId])
}

model SaltEdgeConnection {
  id               Int              @id @default(autoincrement())
  connectionId     String
  consentId        String
  partnerConsentId String
  consentExpiresAt DateTime?
  providerName     String
  providerId       String
  status           ConnectionStatus @default(ACTIVE)

  raw Json?

  company   Company @relation(fields: [companyId], references: [id], onDelete: Cascade)
  companyId Int

  saltEdgeAccounts SaltEdgeAccount[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([connectionId])
  @@index([companyId])
}

model PlaidAccount {
  id       Int      @id @default(autoincrement())
  plaidId  String
  name     String
  currency Currency

  raw Json?

  item   PlaidItem @relation(fields: [itemId], references: [id], onDelete: Cascade)
  itemId Int

  bankAccount   BankAccount @relation(fields: [bankAccountId], references: [id], onDelete: Cascade)
  bankAccountId Int

  transactions Transaction[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([plaidId])
  @@index([itemId])
  @@index([bankAccountId])
}

model PlaidItem {
  id              Int              @id @default(autoincrement())
  plaidId         String
  accessToken     String
  institutionName String?
  institutionId   String?
  nextCursor      String?
  status          ConnectionStatus @default(ACTIVE)

  raw Json?

  company   Company @relation(fields: [companyId], references: [id], onDelete: Cascade)
  companyId Int

  plaidAccounts PlaidAccount[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([plaidId])
  @@index([companyId])
}

model Category {
  id          Int           @id @default(autoincrement())
  name        String
  type        FinancialType
  description String?

  company   Company? @relation(fields: [companyId], references: [id], onDelete: Cascade)
  companyId Int?

  leafCategories LeafCategory[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([name, type, companyId])
  @@index([companyId])
}

model LeafCategory {
  id          Int                      @id @default(autoincrement())
  name        String
  type        FinancialType
  specialType LeafCategorySpecialType?
  description String?

  company   Company? @relation(fields: [companyId], references: [id], onDelete: Cascade)
  companyId Int?

  category   Category @relation(fields: [categoryId], references: [id], onDelete: Cascade)
  categoryId Int

  leafCategoriesOnIndustries LeafCategoriesOnIndustries[]

  transactions Transaction[]

  invoices Invoice[]

  bills Bill[]

  accountRecurringPatterns AccountRecurringPattern[]

  accountSeasonalities AccountSeasonality[]

  accountTrends AccountTrend[]

  accountMonthlyForecasts AccountMonthlyForecast[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([name, categoryId, companyId])
  @@index([companyId])
  @@index([categoryId])
}

model LeafCategoriesOnIndustries {
  leafCategory   LeafCategory @relation(fields: [leafCategoryId], references: [id], onDelete: Cascade)
  leafCategoryId Int

  industry   CompanyIndustry @relation(fields: [industryId], references: [id], onDelete: Cascade)
  industryId Int

  @@id([leafCategoryId, industryId])
}

model Transaction {
  id                        Int            @id @default(autoincrement())
  amount                    Float
  convertedAmount           Json
  paymentMethod             PaymentMethod?
  type                      FinancialType
  currency                  Currency
  date                      DateTime
  description               String?        @db.Citext
  ownerAccountNumber        String?
  ownerAccountName          String?
  ownerAccountCurrency      Currency?
  ownerBankName             String?
  categoryConfidenceScore   Float?         @default(0)
  enrichmentConfidenceScore Float?         @default(0)
  excludeFromAnalytics      Boolean        @default(false)

  accountNumber String? @db.Citext // sender's/receiver's IBAN
  accountName   String? @db.Citext

  transactionId String?
  hash          String?
  bookDate      DateTime?

  raw Json?

  company   Company @relation(fields: [companyId], references: [id], onDelete: Cascade)
  companyId Int

  bankAccount   BankAccount @relation(fields: [bankAccountId], references: [id], onDelete: Cascade)
  bankAccountId Int

  saltEdgeAccount   SaltEdgeAccount? @relation(fields: [saltEdgeAccountId], references: [id], onDelete: SetNull)
  saltEdgeAccountId Int?

  plaidAccount   PlaidAccount? @relation(fields: [plaidAccountId], references: [id], onDelete: SetNull)
  plaidAccountId Int?

  file   File? @relation(fields: [fileId], references: [id], onDelete: SetNull)
  fileId Int?

  privateMerchant   PrivateMerchant? @relation(fields: [privateMerchantId], references: [id], onDelete: SetNull)
  privateMerchantId Int?

  leafCategory   LeafCategory? @relation(fields: [leafCategoryId], references: [id], onDelete: SetNull)
  leafCategoryId Int?

  labels TransactionsOnLabels[]

  invoices TransactionsOnInvoices[]

  bills TransactionsOnBills[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([transactionId])
  @@index([hash], type: Hash)
  @@index([companyId])
  @@index([bankAccountId])
  @@index([saltEdgeAccountId])
  @@index([plaidAccountId])
  @@index([fileId])
  @@index([privateMerchantId])
  @@index([leafCategoryId])
}

model UnprocessedTransaction {
  id                   Int           @id @default(autoincrement())
  amount               Float
  convertedAmount      Json
  type                 FinancialType
  currency             Currency
  date                 DateTime
  description          String?       @db.Citext
  ownerAccountNumber   String?
  ownerAccountName     String?
  ownerAccountCurrency Currency?
  ownerBankName        String?

  accountNumber String?
  accountName   String?

  transactionId String?
  hash          String?
  bookDate      DateTime?

  bankAccountId     Int
  saltEdgeAccountId Int?
  plaidAccountId    Int?
  fileId            Int?

  raw Json?

  company   Company @relation(fields: [companyId], references: [id], onDelete: Cascade)
  companyId Int

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([transactionId])
  @@index([hash], type: Hash)
  @@index([companyId])
}

model BankCardNumber {
  id     Int    @id @default(autoincrement())
  name   String
  number String

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([number])
}

model PrivateMerchant {
  id                   Int          @id @default(autoincrement())
  displayName          String       @db.Citext
  excludeFromAnalytics Boolean      @default(false)
  type                 MerchantType
  info                 String?

  company   Company @relation(fields: [companyId], references: [id], onDelete: Cascade)
  companyId Int

  transactions Transaction[]

  labels PrivateMerchantsOnLabels[]

  invoices Invoice[]

  bills Bill[]

  contexts AccountContext[]

  forecasts AccountForecast[]

  unprocessedInvoices UnprocessedInvoice[]

  unprocessedBills UnprocessedBill[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([companyId])
}

model File {
  id              Int                  @id @default(autoincrement())
  key             String
  version         String
  name            String
  type            FileType
  statementFormat FileStatementFormat?
  isProcessed     Boolean              @default(false)
  mimeType        String

  company   Company @relation(fields: [companyId], references: [id], onDelete: Cascade)
  companyId Int

  bankAccount   BankAccount? @relation(fields: [bankAccountId], references: [id], onDelete: Cascade)
  bankAccountId Int?

  transactions Transaction[]

  invoices Invoice[]

  bills Bill[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([companyId])
  @@index([bankAccountId])
}

model WidgetRow {
  id       Int @id @default(autoincrement())
  size     Int
  position Int

  company   Company @relation(fields: [companyId], references: [id], onDelete: Cascade)
  companyId Int

  user   User @relation(fields: [userId], references: [id])
  userId Int

  widgets Widget[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([companyId])
  @@index([userId])
}

model Widget {
  id          Int     @id @default(autoincrement())
  position    Int
  prompt      String
  meta        Json
  aiSessionId String?

  widgetRow   WidgetRow @relation(fields: [widgetRowId], references: [id], onDelete: Cascade)
  widgetRowId Int

  widgetInsights WidgetInsight[] @relation(name: "widgetToWidgetInsights")

  sourceWidgetInsight WidgetInsight? @relation(name: "createdWidgetToSourceWidgetInsight")

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([widgetRowId])
}

model WidgetPreview {
  id          Int     @id @default(autoincrement())
  aiId        String?
  prompt      String
  meta        Json
  aiSessionId String?

  company   Company @relation(fields: [companyId], references: [id], onDelete: Cascade)
  companyId Int

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([aiId])
  @@index([companyId])
}

model WidgetInsight {
  id           Int                  @id @default(autoincrement())
  title        String
  description  String
  action       WidgetInsightAction?
  actionPrompt String?
  type         WidgetInsightType

  company   Company @relation(fields: [companyId], references: [id], onDelete: Cascade)
  companyId Int

  widget   Widget @relation(name: "widgetToWidgetInsights", fields: [widgetId], references: [id], onDelete: Cascade)
  widgetId Int

  createdWidget   Widget? @relation(name: "createdWidgetToSourceWidgetInsight", fields: [createdWidgetId], references: [id])
  createdWidgetId Int?

  createdAlert   Alert? @relation(fields: [createdAlertId], references: [id])
  createdAlertId Int?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([createdWidgetId])
  @@unique([createdAlertId])
  @@index([companyId])
  @@index([widgetId])
}

model AlertGroup {
  id   Int            @id @default(autoincrement())
  name String
  type AlertGroupType

  alerts Alert[]

  alertPreviews AlertPreview[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Alert {
  id                             Int                     @id @default(autoincrement())
  title                          String
  prompt                         String
  meta                           Json
  isActive                       Boolean                 @default(true)
  externalDelivery               AlertExternalDelivery[]
  lastActivity                   DateTime?
  evaluationStartDate            DateTime?               @db.Date
  evaluationEndDate              DateTime?               @db.Date
  lastDisplayQueriesDataItemsIds Json?
  isPauseEnabled                 Boolean                 @default(false)
  maxConsecutiveTriggers         Int                     @default(3)
  currentTriggerCount            Int                     @default(0)
  isPaused                       Boolean                 @default(false)

  company   Company @relation(fields: [companyId], references: [id], onDelete: Cascade)
  companyId Int

  user   User @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId Int

  alertGroup   AlertGroup @relation(fields: [alertGroupId], references: [id])
  alertGroupId Int

  alertNotifications AlertNotification[]

  sourceWidgetInsight WidgetInsight?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([companyId])
  @@index([userId])
  @@index([alertGroupId])
}

model AlertPreview {
  id     Int    @id @default(autoincrement())
  aiId   String
  title  String
  prompt String
  meta   Json

  company   Company @relation(fields: [companyId], references: [id], onDelete: Cascade)
  companyId Int

  user   User @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId Int

  alertGroup   AlertGroup @relation(fields: [alertGroupId], references: [id])
  alertGroupId Int

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([aiId])
  @@index([companyId])
  @@index([userId])
  @@index([alertGroupId])
}

model AlertNotification {
  id          Int      @id @default(autoincrement())
  title       String
  isViewed    Boolean  @default(false)
  dataKey     String
  dataVersion String
  priority    Priority @default(HIGH)

  company   Company @relation(fields: [companyId], references: [id], onDelete: Cascade)
  companyId Int

  user   User @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId Int

  alert   Alert @relation(fields: [alertId], references: [id], onDelete: Cascade)
  alertId Int

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([companyId])
  @@index([userId])
  @@index([alertId])
}

model FinancialInsightNotification {
  id       Int      @id @default(autoincrement())
  title    String
  isViewed Boolean  @default(false)
  priority Priority @default(MEDIUM)

  company   Company @relation(fields: [companyId], references: [id], onDelete: Cascade)
  companyId Int

  financialInsight   FinancialInsight @relation(fields: [financialInsightId], references: [id], onDelete: Cascade)
  financialInsightId Int

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([financialInsightId])
  @@index([companyId])
}

model FinancialInsight {
  id                      Int                  @id @default(autoincrement())
  summary                 String
  type                    FinancialInsightType
  startDate               DateTime
  endDate                 DateTime
  expenseTransactionCount Int
  expenseAmount           Float
  expenseChange           Float?
  incomeTransactionCount  Int
  incomeAmount            Float
  incomeChange            Float?
  profitAmount            Float
  profitChange            Float?
  transactionsKey         String?
  transactionsVersion     String?
  currency                Currency
  sharedHash              String?

  company   Company @relation(fields: [companyId], references: [id], onDelete: Cascade)
  companyId Int

  financialInsightsNotification FinancialInsightNotification?

  financialInsightSuggestions FinancialInsightSuggestion[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([sharedHash])
  @@index([companyId])
}

model FinancialInsightSuggestion {
  id           Int                    @id @default(autoincrement())
  title        String
  description  String
  action       FinancialInsightAction
  actionPrompt String?

  financialInsight   FinancialInsight @relation(fields: [financialInsightId], references: [id], onDelete: Cascade)
  financialInsightId Int

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([financialInsightId])
}

model GenericNotification {
  id          Int      @id @default(autoincrement())
  title       String
  isViewed    Boolean  @default(false)
  description String?
  content     String
  sharedHash  String?
  priority    Priority @default(LOW)

  company   Company @relation(fields: [companyId], references: [id], onDelete: Cascade)
  companyId Int

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([sharedHash])
  @@index([companyId])
}

model SystemNotification {
  id       Int      @id @default(autoincrement())
  title    String
  isViewed Boolean  @default(false)
  priority Priority @default(HIGH)

  company   Company @relation(fields: [companyId], references: [id], onDelete: Cascade)
  companyId Int

  user   User @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId Int

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([companyId])
  @@index([userId])
}

model ExchangeRateDate {
  id   Int      @id @default(autoincrement())
  date DateTime

  exchangeRates ExchangeRate[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([date])
}

model ExchangeRate {
  id             Int      @id @default(autoincrement())
  baseCurrency   Currency
  targetCurrency Currency
  rate           Float
  reverseRate    Float

  exchangeRateDate   ExchangeRateDate @relation(fields: [exchangeRateDateId], references: [id])
  exchangeRateDateId Int

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([baseCurrency, targetCurrency, exchangeRateDateId])
  @@index([exchangeRateDateId])
}

model TransactionsOnLabels {
  transaction   Transaction @relation(fields: [transactionId], references: [id], onDelete: Cascade)
  transactionId Int

  label   Label @relation(fields: [labelId], references: [id], onDelete: Cascade)
  labelId Int

  @@id([transactionId, labelId])
}

model PrivateMerchantsOnLabels {
  privateMerchant   PrivateMerchant @relation(fields: [privateMerchantId], references: [id], onDelete: Cascade)
  privateMerchantId Int

  label   Label @relation(fields: [labelId], references: [id], onDelete: Cascade)
  labelId Int

  @@id([privateMerchantId, labelId])
}

model InvoicesOnLabels {
  invoice   Invoice @relation(fields: [invoiceId], references: [id], onDelete: Cascade)
  invoiceId Int

  label   Label @relation(fields: [labelId], references: [id], onDelete: Cascade)
  labelId Int

  @@id([invoiceId, labelId])
}

model BillsOnLabels {
  bill   Bill @relation(fields: [billId], references: [id], onDelete: Cascade)
  billId Int

  label   Label @relation(fields: [labelId], references: [id], onDelete: Cascade)
  labelId Int

  @@id([billId, labelId])
}

model Label {
  id   Int    @id @default(autoincrement())
  name String

  company   Company @relation(fields: [companyId], references: [id], onDelete: Cascade)
  companyId Int

  transactions TransactionsOnLabels[]

  privateMerchantsOnLabels PrivateMerchantsOnLabels[]

  invoices InvoicesOnLabels[]

  bills BillsOnLabels[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([companyId, name])
}

model PromptSuggestion {
  id     Int                  @id @default(autoincrement())
  prompt String
  type   PromptSuggestionType

  company   Company @relation(fields: [companyId], references: [id], onDelete: Cascade)
  companyId Int

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([companyId])
}

model Country {
  id              Int      @id @default(autoincrement())
  name            String
  code            String
  region          String
  flagEmoji       String
  defaultCurrency Currency @default(EUR)

  companies Company[]

  companyType CompanyType[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model TransactionsOnInvoices {
  confidenceScore Float? @default(0)

  transaction   Transaction @relation(fields: [transactionId], references: [id], onDelete: Cascade)
  transactionId Int

  invoice   Invoice @relation(fields: [invoiceId], references: [id], onDelete: Cascade)
  invoiceId Int

  @@id([transactionId, invoiceId])
}

model Invoice {
  id                      Int       @id @default(autoincrement())
  categoryConfidenceScore Float?    @default(0)
  qbId                    String?
  accountName             String?
  currency                Currency
  amount                  Float
  tax                     Float     @default(0)
  totalAmount             Float
  convertedTotalAmount    Json
  isPaid                  Boolean   @default(false)
  balance                 Float
  docNumber               String    @default("") @db.Citext
  lineDescriptions        String
  dueDate                 DateTime?
  issueDate               DateTime?
  title                   String?
  note                    String?
  taxPercentage           Float?
  raw                     Json?

  company   Company @relation(fields: [companyId], references: [id], onDelete: Cascade)
  companyId Int

  privateMerchant   PrivateMerchant? @relation(fields: [privateMerchantId], references: [id], onDelete: SetNull)
  privateMerchantId Int?

  quickBookConnection   QuickBookConnection? @relation(fields: [quickBookConnectionId], references: [id], onDelete: SetNull)
  quickBookConnectionId Int?

  file   File? @relation(fields: [fileId], references: [id], onDelete: SetNull)
  fileId Int?

  leafCategory   LeafCategory? @relation(fields: [leafCategoryId], references: [id], onDelete: SetNull)
  leafCategoryId Int?

  transactions TransactionsOnInvoices[]

  lines InvoiceLine[]

  labels InvoicesOnLabels[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([qbId, companyId])
  @@index([companyId])
  @@index([privateMerchantId])
  @@index([quickBookConnectionId])
  @@index([fileId])
  @@index([leafCategoryId])
}

model InvoiceLine {
  id          Int             @id @default(autoincrement())
  amount      Float
  tax         Float           @default(0)
  totalAmount Float
  type        InvoiceLineType
  description String?
  unitPrice   Float?          @default(0)
  quantity    Float?          @default(0)
  raw         Json?

  company   Company @relation(fields: [companyId], references: [id], onDelete: Cascade)
  companyId Int

  invoice   Invoice @relation(fields: [invoiceId], references: [id], onDelete: Cascade)
  invoiceId Int

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([companyId])
  @@index([invoiceId])
}

model UnprocessedInvoice {
  id                      Int       @id @default(autoincrement())
  categoryConfidenceScore Float?    @default(0)
  qbId                    String?
  accountName             String?
  currency                Currency
  amount                  Float
  tax                     Float     @default(0)
  totalAmount             Float
  convertedTotalAmount    Json
  isPaid                  Boolean   @default(false)
  balance                 Float
  docNumber               String    @default("") @db.Citext
  lineDescriptions        String
  dueDate                 DateTime?
  issueDate               DateTime?
  title                   String?
  note                    String?
  taxPercentage           Float?
  lines                   Json?
  raw                     Json?

  quickBookConnectionId Int?
  fileId                Int?
  leafCategoryId        Int?
  transactionId         Int?
  labelIds              Int[]

  company   Company @relation(fields: [companyId], references: [id], onDelete: Cascade)
  companyId Int

  privateMerchant   PrivateMerchant? @relation(fields: [privateMerchantId], references: [id], onDelete: SetNull)
  privateMerchantId Int?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([companyId])
  @@index([privateMerchantId])
}

model TransactionsOnBills {
  confidenceScore Float? @default(0)

  transaction   Transaction @relation(fields: [transactionId], references: [id], onDelete: Cascade)
  transactionId Int

  bill   Bill @relation(fields: [billId], references: [id], onDelete: Cascade)
  billId Int

  @@id([transactionId, billId])
}

model Bill {
  id                      Int                  @id @default(autoincrement())
  categoryConfidenceScore Float?               @default(0)
  qbId                    String?
  accountName             String?
  type                    BillType
  purchasePaymentType     PurchasePaymentType?
  currency                Currency
  amount                  Float
  tax                     Float                @default(0)
  totalAmount             Float
  convertedTotalAmount    Json
  isPaid                  Boolean              @default(false)
  docNumber               String               @default("") @db.Citext
  lineDescriptions        String
  dueDate                 DateTime?
  issueDate               DateTime?
  title                   String?
  note                    String?
  taxPercentage           Float?
  raw                     Json?

  company   Company @relation(fields: [companyId], references: [id], onDelete: Cascade)
  companyId Int

  privateMerchant   PrivateMerchant? @relation(fields: [privateMerchantId], references: [id], onDelete: SetNull)
  privateMerchantId Int?

  quickBookConnection   QuickBookConnection? @relation(fields: [quickBookConnectionId], references: [id], onDelete: SetNull)
  quickBookConnectionId Int?

  file   File? @relation(fields: [fileId], references: [id], onDelete: SetNull)
  fileId Int?

  leafCategory   LeafCategory? @relation(fields: [leafCategoryId], references: [id], onDelete: SetNull)
  leafCategoryId Int?

  transactions TransactionsOnBills[]

  lines BillLine[]

  labels BillsOnLabels[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([qbId, companyId, type])
  @@index([companyId])
  @@index([privateMerchantId])
  @@index([quickBookConnectionId])
  @@index([fileId])
  @@index([leafCategoryId])
}

model BillLine {
  id          Int          @id @default(autoincrement())
  amount      Float
  tax         Float        @default(0)
  totalAmount Float
  type        BillLineType
  description String?
  unitPrice   Float?       @default(0)
  quantity    Float?       @default(0)
  raw         Json?

  company   Company @relation(fields: [companyId], references: [id], onDelete: Cascade)
  companyId Int

  bill   Bill @relation(fields: [billId], references: [id], onDelete: Cascade)
  billId Int

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([companyId])
  @@index([billId])
}

model UnprocessedBill {
  id                      Int                  @id @default(autoincrement())
  categoryConfidenceScore Float?               @default(0)
  qbId                    String?
  accountName             String?
  type                    BillType
  purchasePaymentType     PurchasePaymentType?
  currency                Currency
  amount                  Float
  tax                     Float                @default(0)
  totalAmount             Float
  convertedTotalAmount    Json
  isPaid                  Boolean              @default(false)
  docNumber               String               @default("") @db.Citext
  lineDescriptions        String
  dueDate                 DateTime?
  issueDate               DateTime?
  title                   String?
  note                    String?
  taxPercentage           Float?
  lines                   Json?
  raw                     Json?

  quickBookConnectionId Int?
  fileId                Int?
  leafCategoryId        Int?
  transactionId         Int?
  labelIds              Int[]

  company   Company @relation(fields: [companyId], references: [id], onDelete: Cascade)
  companyId Int

  privateMerchant   PrivateMerchant? @relation(fields: [privateMerchantId], references: [id], onDelete: SetNull)
  privateMerchantId Int?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([companyId])
  @@index([privateMerchantId])
}

model QuickBookConnection {
  id                   Int              @id @default(autoincrement())
  realmId              String
  accessToken          String?
  refreshToken         String?
  refreshTokenExpireAt DateTime?
  status               ConnectionStatus @default(ACTIVE)

  company   Company @relation(fields: [companyId], references: [id], onDelete: Cascade)
  companyId Int

  invoices Invoice[]

  bills Bill[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([companyId])
  @@unique([realmId])
}

model Context {
  id              Int           @id @default(autoincrement())
  currency        Currency
  startPeriod     String
  endPeriod       String
  status          ContextStatus @default(ACCOUNT_CONTEXT)
  lastProcessedId Int           @default(0)

  company   Company @relation(fields: [companyId], references: [id], onDelete: Cascade)
  companyId Int

  accountContexts AccountContext[]

  forecasts Forecast[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([companyId])
}

model AccountContext {
  id                      Int            @id @default(autoincrement())
  primaryDrivers          String[]
  keyRisks                String[]
  accountStatus           AccountStatus?
  lastActivityDate        String?
  accountSummary          String?
  reasoning               String?
  accountSummaryReasoning String?

  privateMerchant   PrivateMerchant @relation(fields: [privateMerchantId], references: [id], onDelete: Cascade)
  privateMerchantId Int

  context   Context @relation(fields: [contextId], references: [id], onDelete: Cascade)
  contextId Int

  recurringPatterns AccountRecurringPattern[]

  seasonalities AccountSeasonality[]

  trends AccountTrend[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([privateMerchantId, contextId])
}

model AccountRecurringPattern {
  id                   Int                @id @default(autoincrement())
  notes                String
  amount               Float
  frequency            RecurringFrequency
  scopeType            PatternScopeType   @default(LEAF_CATEGORY)
  scopeLeafCategoryIds Int[]
  scopeDescription     String?
  typicalDayOfMonth    Int?
  dayVariance          Int?
  amountVariance       Float?
  firstObserved        String?
  lastObserved         String?
  occurrenceCount      Int?

  leafCategory   LeafCategory? @relation(fields: [leafCategoryId], references: [id], onDelete: Cascade)
  leafCategoryId Int?

  context   AccountContext @relation(fields: [contextId], references: [id], onDelete: Cascade)
  contextId Int

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([leafCategoryId])
  @@index([contextId])
}

model AccountSeasonality {
  id                   Int              @id @default(autoincrement())
  notes                String
  scopeType            PatternScopeType @default(LEAF_CATEGORY)
  peakPeriods          String[]
  troughPeriods        String[]
  scopeLeafCategoryIds Int[]
  scopeDescription     String?
  typicalAmount        Float?
  amountVariance       Float?
  peakMultiplier       Float?
  cyclesObserved       Int?

  leafCategory   LeafCategory? @relation(fields: [leafCategoryId], references: [id], onDelete: Cascade)
  leafCategoryId Int?

  context   AccountContext @relation(fields: [contextId], references: [id], onDelete: Cascade)
  contextId Int

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([leafCategoryId])
  @@index([contextId])
}

model AccountTrend {
  id                   Int              @id @default(autoincrement())
  trendType            TrendType        @default(IRREGULAR)
  notes                String
  scopeType            PatternScopeType @default(LEAF_CATEGORY)
  scopeLeafCategoryIds Int[]
  growthRate           Float?
  lastActivePeriod     String?
  scopeDescription     String?
  volatility           Float?
  trendStartPeriod     String?

  leafCategory   LeafCategory? @relation(fields: [leafCategoryId], references: [id], onDelete: Cascade)
  leafCategoryId Int?

  context   AccountContext @relation(fields: [contextId], references: [id], onDelete: Cascade)
  contextId Int

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([leafCategoryId])
  @@index([contextId])
}

model Forecast {
  id          Int     @id @default(autoincrement())
  name        String
  startPeriod String
  endPeriod   String
  isBase      Boolean @default(false)
  isTracked   Boolean @default(false)

  company   Company @relation(fields: [companyId], references: [id], onDelete: Cascade)
  companyId Int

  context   Context @relation(fields: [contextId], references: [id], onDelete: Cascade)
  contextId Int

  accountForecasts AccountForecast[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // There is also a partial unique index on (companyId and isBase = true) - Forecast_companyId_isBase_idx
  @@unique([companyId, name])
  @@index([companyId])
  @@index([contextId])
}

model AccountForecast {
  id                  Int      @id @default(autoincrement())
  privateMerchantName String
  majorAssumptions    String[]
  keyRisks            String[]
  reasoning           String?

  privateMerchant   PrivateMerchant? @relation(fields: [privateMerchantId], references: [id], onDelete: Cascade)
  privateMerchantId Int?

  forecast   Forecast @relation(fields: [forecastId], references: [id], onDelete: Cascade)
  forecastId Int

  monthlyForecasts AccountMonthlyForecast[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([privateMerchantId, forecastId])
  @@index([privateMerchantId])
  @@index([forecastId])
}

model AccountMonthlyForecast {
  id        Int     @id @default(autoincrement())
  period    String
  amount    Float
  reasoning String?
  raw       Json?

  leafCategory   LeafCategory? @relation(fields: [leafCategoryId], references: [id], onDelete: Cascade)
  leafCategoryId Int?

  forecast   AccountForecast @relation(fields: [forecastId], references: [id], onDelete: Cascade)
  forecastId Int

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([forecastId, period, leafCategoryId])
  @@index([leafCategoryId])
  @@index([forecastId])
}

model AiAssistantConversation {
  id          Int    @id @default(autoincrement())
  aiSessionId String
  title       String

  company   Company @relation(fields: [companyId], references: [id], onDelete: Cascade)
  companyId Int

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([companyId])
}

model AppSubscriptionPlan {
  id                 Int               @id @default(autoincrement())
  stripeId           String
  name               String
  restrictedFeatures AppUsageFeature[]

  limits AppSubscriptionPlanLimit[]

  subscriptions AppSubscription[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([stripeId])
}

model AppSubscriptionPlanLimit {
  id Int @id @default(autoincrement())

  max           Int?
  feature       AppUsageFeature
  resetInterval AppUsageResetInterval?

  plan   AppSubscriptionPlan @relation(fields: [planId], references: [id], onDelete: Cascade)
  planId Int

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([planId])
}

model AppSubscription {
  id          Int      @id @default(autoincrement())
  stripeId    String
  renewalDate DateTime

  company   Company @relation(fields: [companyId], references: [id], onDelete: Cascade)
  companyId Int

  plan   AppSubscriptionPlan @relation(fields: [planId], references: [id])
  planId Int

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([stripeId])
  @@unique([companyId])
  @@index([planId])
}

// Singleton
model AppFreePlan {
  id                 Int               @id @default(1)
  restrictedFeatures AppUsageFeature[]

  limits AppFreePlanLimit[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model AppFreePlanLimit {
  id Int @id @default(autoincrement())

  max           Int?
  feature       AppUsageFeature
  resetInterval AppUsageResetInterval?

  plan   AppFreePlan @relation(fields: [planId], references: [id], onDelete: Cascade)
  planId Int

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([planId])
}

model AppUsage {
  id Int @id @default(autoincrement())

  max           Int?
  usage         Int                    @default(0)
  maxOverride   Int?
  feature       AppUsageFeature
  resetInterval AppUsageResetInterval?

  company   Company @relation(fields: [companyId], references: [id], onDelete: Cascade)
  companyId Int

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([companyId, feature])
}

model CompanyDisplayFlag {
  id Int @id @default(autoincrement())

  showFreeTrialExpired  Boolean @default(false)
  showBaseForecastReady Boolean @default(false)

  company   Company @relation(fields: [companyId], references: [id], onDelete: Cascade)
  companyId Int

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([companyId])
}

enum OnboardingStep {
  USER_INFO
  COMPANY_INFO
  REVIEW
  SUCCESS
  COMPLETED
}

enum FinancialType {
  EXPENSE
  INCOME
}

enum LeafCategorySpecialType {
  SALARIES_AND_WAGES
  CREDIT_CARD_CLEARING
}

enum FileType {
  TRANSACTIONS
  INVOICES
  BILLS
  INVOICE_DOCUMENTS
  BILL_DOCUMENTS
}

enum FileStatementFormat {
  CUSTOM
  MN
  CAMT052
  CAMT053
  CAMT054
  MT940
  MT942
  REVOLUT
}

enum SocialLoginProvider {
  GOOGLE
}

enum AlertExternalDelivery {
  EMAIL
}

enum AlertGroupType {
  PRIMARY
  FALLBACK
}

enum BankAccountType {
  DEPOSITORY
  CREDIT
}

enum Currency {
  EUR
  USD
  GBP
  HRK
  CZK
  DKK
  HUF
  NOK
  PLN
  RSD
  SEK
  CHF
  TRY
  BAM
  AUD
  CAD
}

enum DecimalPoint {
  PERIOD
  COMMA
}

enum DateFormat {
  DD_MM_YYYY
  MM_DD_YYYY
}

enum PaymentMethod {
  BANK
  CARD
  CASH
}

enum MerchantType {
  PRIVATE
  BUSINESS
}

enum PromptSuggestionType {
  WIDGET
  AI_ASSISTANT
}

enum WidgetInsightType {
  PRO
  CON
  SUGGESTION
}

enum WidgetInsightAction {
  CREATE_ALERT
  CREATE_WIDGET
}

enum FinancialInsightAction {
  NONE
  CREATE_ALERT
  CREATE_WIDGET
}

enum FinancialInsightType {
  WEEKLY
  MONTHLY
}

enum ConnectionStatus {
  INACTIVE
  ABOUT_TO_EXPIRE
  ACTIVE
}

enum InvoiceLineType {
  SALES_ITEM
  GROUP_ITEM
  DESCRIPTION_ONLY
  DISCOUNT
  SUB_TOTAL
}

enum BillLineType {
  ITEM_BASED_EXPENSE
  ACCOUNT_BASED_EXPENSE
}

enum BillType {
  BILL
  PURCHASE
}

enum PurchasePaymentType {
  CASH
  CHECK
  CREDIT_CARD
}

enum Priority {
  HIGH
  MEDIUM
  LOW
}

enum CompanyUserStatus {
  TRIAL
  ACTIVE
  READONLY
}

enum AppUsageFeature {
  TRANSACTIONS
  INVOICES_BILLS
  PROMPTS
  FORECAST
}

enum AppUsageResetInterval {
  MONTHLY
  YEARLY
}

enum AttentionLevel {
  NONE
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum TrendType {
  IRREGULAR
  GROWING
  DECLINING
  STABLE
  TERMINATED
  VOLATILE
}

enum RecurringFrequency {
  MONTHLY
  QUARTERLY
  HALFYEAR
  ANNUAL
  CUSTOM
}

enum PatternScopeType {
  LEAF_CATEGORY
  ACCOUNT_WIDE
}

enum AccountStatus {
  ACTIVE
  INACTIVE
  TERMINATED
  UNKNOWN
}

enum ContextStatus {
  ACCOUNT_CONTEXT
  ACCOUNT_FORECAST
  DONE
}
