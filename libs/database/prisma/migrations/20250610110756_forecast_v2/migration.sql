/*
  Warnings:

  - The values [LEAF_CATEGORY_CONTEXT,CATEGORY_CONTEXT,COMPANY_CONTEXT,LEAF_CATEGORY_FORECAST,CATEGORY_FORECAST,COMPANY_FORECAST] on the enum `ContextStatus` will be removed. If these variants are still used in the database, this will fail.
  - You are about to drop the column `summary` on the `AccountForecast` table. All the data in the column will be lost.
  - You are about to drop the `AccountHumanInsightRequest` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `AccountMonthlyContext` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `CategoryContext` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `CategoryForecast` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `CategoryMonthlyContext` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `CategoryMonthlyForecast` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `CompanyContext` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `CompanyForecast` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `CompanyForecastRiskFactors` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `CompanyKeyDriver` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `CompanyMonthlyContext` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `CompanyMonthlyForecast` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `CompanyRiskArea` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `LeafCategoryContext` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `LeafCategoryForecast` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `LeafCategoryMonthlyContext` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `LeafCategoryMonthlyForecast` table. If the table is not empty, all the data it contains will be lost.
  - Added the required column `privateMerchantName` to the `AccountForecast` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "PatternScopeType" AS ENUM ('LEAF_CATEGORY', 'ACCOUNT_WIDE');

-- CreateEnum
CREATE TYPE "AccountStatus" AS ENUM ('ACTIVE', 'INACTIVE', 'TERMINATED', 'UNKNOWN');

-- AlterEnum
BEGIN;
CREATE TYPE "ContextStatus_new" AS ENUM ('ACCOUNT_CONTEXT', 'ACCOUNT_FORECAST', 'DONE');
ALTER TABLE "Context" ALTER COLUMN "status" DROP DEFAULT;
ALTER TABLE "Context" ALTER COLUMN "status" TYPE "ContextStatus_new" USING ("status"::text::"ContextStatus_new");
ALTER TYPE "ContextStatus" RENAME TO "ContextStatus_old";
ALTER TYPE "ContextStatus_new" RENAME TO "ContextStatus";
DROP TYPE "ContextStatus_old";
ALTER TABLE "Context" ALTER COLUMN "status" SET DEFAULT 'ACCOUNT_CONTEXT';
COMMIT;

-- DropForeignKey
ALTER TABLE "AccountHumanInsightRequest" DROP CONSTRAINT "AccountHumanInsightRequest_contextId_fkey";

-- DropForeignKey
ALTER TABLE "AccountMonthlyContext" DROP CONSTRAINT "AccountMonthlyContext_contextId_fkey";

-- DropForeignKey
ALTER TABLE "AccountMonthlyForecast" DROP CONSTRAINT "AccountMonthlyForecast_leafCategoryId_fkey";

-- DropForeignKey
ALTER TABLE "AccountRecurringPattern" DROP CONSTRAINT "AccountRecurringPattern_leafCategoryId_fkey";

-- DropForeignKey
ALTER TABLE "AccountSeasonality" DROP CONSTRAINT "AccountSeasonality_leafCategoryId_fkey";

-- DropForeignKey
ALTER TABLE "AccountTrend" DROP CONSTRAINT "AccountTrend_leafCategoryId_fkey";

-- DropForeignKey
ALTER TABLE "CategoryContext" DROP CONSTRAINT "CategoryContext_categoryId_fkey";

-- DropForeignKey
ALTER TABLE "CategoryContext" DROP CONSTRAINT "CategoryContext_contextId_fkey";

-- DropForeignKey
ALTER TABLE "CategoryForecast" DROP CONSTRAINT "CategoryForecast_categoryId_fkey";

-- DropForeignKey
ALTER TABLE "CategoryForecast" DROP CONSTRAINT "CategoryForecast_forecastId_fkey";

-- DropForeignKey
ALTER TABLE "CategoryMonthlyContext" DROP CONSTRAINT "CategoryMonthlyContext_contextId_fkey";

-- DropForeignKey
ALTER TABLE "CategoryMonthlyForecast" DROP CONSTRAINT "CategoryMonthlyForecast_forecastId_fkey";

-- DropForeignKey
ALTER TABLE "CompanyContext" DROP CONSTRAINT "CompanyContext_companyId_fkey";

-- DropForeignKey
ALTER TABLE "CompanyContext" DROP CONSTRAINT "CompanyContext_contextId_fkey";

-- DropForeignKey
ALTER TABLE "CompanyForecast" DROP CONSTRAINT "CompanyForecast_forecastId_fkey";

-- DropForeignKey
ALTER TABLE "CompanyForecastRiskFactors" DROP CONSTRAINT "CompanyForecastRiskFactors_forecastId_fkey";

-- DropForeignKey
ALTER TABLE "CompanyKeyDriver" DROP CONSTRAINT "CompanyKeyDriver_contextId_fkey";

-- DropForeignKey
ALTER TABLE "CompanyMonthlyContext" DROP CONSTRAINT "CompanyMonthlyContext_contextId_fkey";

-- DropForeignKey
ALTER TABLE "CompanyMonthlyForecast" DROP CONSTRAINT "CompanyMonthlyForecast_forecastId_fkey";

-- DropForeignKey
ALTER TABLE "CompanyRiskArea" DROP CONSTRAINT "CompanyRiskArea_contextId_fkey";

-- DropForeignKey
ALTER TABLE "LeafCategoryContext" DROP CONSTRAINT "LeafCategoryContext_contextId_fkey";

-- DropForeignKey
ALTER TABLE "LeafCategoryContext" DROP CONSTRAINT "LeafCategoryContext_leafCategoryId_fkey";

-- DropForeignKey
ALTER TABLE "LeafCategoryForecast" DROP CONSTRAINT "LeafCategoryForecast_forecastId_fkey";

-- DropForeignKey
ALTER TABLE "LeafCategoryForecast" DROP CONSTRAINT "LeafCategoryForecast_leafCategoryId_fkey";

-- DropForeignKey
ALTER TABLE "LeafCategoryMonthlyContext" DROP CONSTRAINT "LeafCategoryMonthlyContext_contextId_fkey";

-- DropForeignKey
ALTER TABLE "LeafCategoryMonthlyForecast" DROP CONSTRAINT "LeafCategoryMonthlyForecast_forecastId_fkey";

ALTER TABLE "Forecast" ADD COLUMN     "isTracked" BOOLEAN NOT NULL DEFAULT false;

ALTER TABLE "CompanyDisplayFlag" ADD COLUMN     "showBaseForecastReady" BOOLEAN NOT NULL DEFAULT false;

-- AlterTable
ALTER TABLE "AccountContext" ADD COLUMN     "accountStatus" "AccountStatus",
ADD COLUMN     "accountSummary" TEXT,
ADD COLUMN     "accountSummaryReasoning" TEXT,
ADD COLUMN     "keyRisks" TEXT[],
ADD COLUMN     "lastActivityDate" TEXT,
ADD COLUMN     "primaryDrivers" TEXT[],
ADD COLUMN     "reasoning" TEXT;

-- AlterTable
ALTER TABLE "AccountForecast" DROP COLUMN "summary",
ADD COLUMN     "keyRisks" TEXT[],
ADD COLUMN     "privateMerchantName" TEXT,
ALTER COLUMN "privateMerchantId" DROP NOT NULL,
ALTER COLUMN "reasoning" DROP NOT NULL;

UPDATE "AccountForecast" SET "privateMerchantName" = (SELECT "displayName" FROM "PrivateMerchant" WHERE "id" = "privateMerchantId");

ALTER TABLE "AccountForecast" ALTER COLUMN "privateMerchantName" SET NOT NULL;

-- AlterTable
ALTER TABLE "AccountMonthlyForecast"
ADD COLUMN     "reasoning" TEXT,
ADD COLUMN     "raw" JSONB,
ALTER COLUMN "leafCategoryId" DROP NOT NULL;

-- AlterTable
ALTER TABLE "AccountRecurringPattern" ADD COLUMN     "amountVariance" DOUBLE PRECISION,
ADD COLUMN     "dayVariance" INTEGER,
ADD COLUMN     "firstObserved" TEXT,
ADD COLUMN     "lastObserved" TEXT,
ADD COLUMN     "occurrenceCount" INTEGER,
ADD COLUMN     "scopeDescription" TEXT,
ADD COLUMN     "scopeLeafCategoryIds" INTEGER[],
ADD COLUMN     "scopeType" "PatternScopeType" NOT NULL DEFAULT 'LEAF_CATEGORY',
ALTER COLUMN "leafCategoryId" DROP NOT NULL;

-- AlterTable
ALTER TABLE "AccountSeasonality" ADD COLUMN     "amountVariance" DOUBLE PRECISION,
ADD COLUMN     "cyclesObserved" INTEGER,
ADD COLUMN     "peakMultiplier" DOUBLE PRECISION,
ADD COLUMN     "scopeDescription" TEXT,
ADD COLUMN     "scopeLeafCategoryIds" INTEGER[],
ADD COLUMN     "scopeType" "PatternScopeType" NOT NULL DEFAULT 'LEAF_CATEGORY',
ADD COLUMN     "typicalAmount" DOUBLE PRECISION,
ALTER COLUMN "leafCategoryId" DROP NOT NULL;

-- AlterTable
ALTER TABLE "AccountTrend" ADD COLUMN     "scopeDescription" TEXT,
ADD COLUMN     "scopeLeafCategoryIds" INTEGER[],
ADD COLUMN     "scopeType" "PatternScopeType" NOT NULL DEFAULT 'LEAF_CATEGORY',
ADD COLUMN     "trendStartPeriod" TEXT,
ADD COLUMN     "volatility" DOUBLE PRECISION,
ALTER COLUMN "leafCategoryId" DROP NOT NULL;

-- DropTable
DROP TABLE "AccountHumanInsightRequest";

-- DropTable
DROP TABLE "AccountMonthlyContext";

-- DropTable
DROP TABLE "CategoryContext";

-- DropTable
DROP TABLE "CategoryForecast";

-- DropTable
DROP TABLE "CategoryMonthlyContext";

-- DropTable
DROP TABLE "CategoryMonthlyForecast";

-- DropTable
DROP TABLE "CompanyContext";

-- DropTable
DROP TABLE "CompanyForecast";

-- DropTable
DROP TABLE "CompanyForecastRiskFactors";

-- DropTable
DROP TABLE "CompanyKeyDriver";

-- DropTable
DROP TABLE "CompanyMonthlyContext";

-- DropTable
DROP TABLE "CompanyMonthlyForecast";

-- DropTable
DROP TABLE "CompanyRiskArea";

-- DropTable
DROP TABLE "LeafCategoryContext";

-- DropTable
DROP TABLE "LeafCategoryForecast";

-- DropTable
DROP TABLE "LeafCategoryMonthlyContext";

-- DropTable
DROP TABLE "LeafCategoryMonthlyForecast";

-- AddForeignKey
ALTER TABLE "AccountRecurringPattern" ADD CONSTRAINT "AccountRecurringPattern_leafCategoryId_fkey" FOREIGN KEY ("leafCategoryId") REFERENCES "LeafCategory"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AccountSeasonality" ADD CONSTRAINT "AccountSeasonality_leafCategoryId_fkey" FOREIGN KEY ("leafCategoryId") REFERENCES "LeafCategory"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AccountTrend" ADD CONSTRAINT "AccountTrend_leafCategoryId_fkey" FOREIGN KEY ("leafCategoryId") REFERENCES "LeafCategory"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AccountMonthlyForecast" ADD CONSTRAINT "AccountMonthlyForecast_leafCategoryId_fkey" FOREIGN KEY ("leafCategoryId") REFERENCES "LeafCategory"("id") ON DELETE CASCADE ON UPDATE CASCADE;

CREATE UNIQUE INDEX "Forecast_companyId_name_key" ON "Forecast"("companyId", "name");
