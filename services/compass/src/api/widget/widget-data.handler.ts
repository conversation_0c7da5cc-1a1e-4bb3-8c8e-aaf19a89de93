import { NotFoundError } from '@compass/common';
import type { Request, Response } from 'express';
import { Redis } from 'ioredis';

import { WidgetCacheService } from '@/src/shared/cache';
import { asyncWrapper } from '@/src/shared/helpers';
import { paramsIdValidator } from '@/src/shared/validators';
import WidgetDataService from '@/src/shared/widget/widget-data.service';

import repository from './widget.repository';

// Initialize services
const redis = new Redis(process.env.REDIS_URL || 'redis://localhost:6379');
const widgetCacheService = new WidgetCacheService(redis);
const widgetDataService = new WidgetDataService(widgetCacheService);

async function getWidgetData(req: Request, res: Response) {
  const widgetId = await paramsIdValidator.parseAsync(req.params.widget_id);
  const { id: userId, companyId } = req.user!;
  const forceRefresh = req.query.refresh === 'true';

  // Verify widget exists and belongs to user
  const widget = await repository.getWidgetById(widgetId, userId);
  if (!widget) throw new NotFoundError('widget', { id: widgetId });

  // Get widget data with caching
  const result = await widgetDataService.getWidgetData(companyId, widgetId, forceRefresh);

  // Add cache headers for debugging
  if (result.metadata.isCached) {
    res.set('X-Cache', 'HIT');
    res.set('X-Cache-Age', result.metadata.ageInMinutes?.toString() || '0');
    res.set('X-Cache-Stale', result.metadata.isStale ? 'true' : 'false');
  } else {
    res.set('X-Cache', 'MISS');
  }

  res.json(result);
}

async function refreshWidgetData(req: Request, res: Response) {
  const widgetId = await paramsIdValidator.parseAsync(req.params.widget_id);
  const { id: userId, companyId } = req.user!;

  // Verify widget exists and belongs to user
  const widget = await repository.getWidgetById(widgetId, userId);
  if (!widget) throw new NotFoundError('widget', { id: widgetId });

  // Force refresh widget data
  const result = await widgetDataService.refreshWidget(companyId, widgetId);

  res.set('X-Cache', 'REFRESHED');

  res.json({
    ...result,
    message: 'Widget data refreshed successfully',
  });
}

export default {
  getWidgetData: asyncWrapper(getWidgetData),
  refreshWidgetData: asyncWrapper(refreshWidgetData),
};
