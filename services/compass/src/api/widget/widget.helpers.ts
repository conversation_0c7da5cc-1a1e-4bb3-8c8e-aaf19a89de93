import { ConflictError, redisService, type Transaction } from '@compass/common';

import repository from './widget.repository';
import type { CreateWidgetBody } from './widget.validators';

async function handleWidgetRowsOnWidgetCreate(
  widgetRow: CreateWidgetBody['widgetRow'],
  userId: number,
  companyId: number,
  tx: Transaction,
) {
  const isCreate = widgetRow.action === 'CREATE';

  if (isCreate) {
    await repository.updateWidgetRows(
      { userId, companyId, position: { gte: widgetRow.position } },
      { position: { increment: 1 } },
      tx,
    );
  }

  return isCreate
    ? { create: { size: widgetRow.size, position: widgetRow.position, companyId, userId } }
    : { connect: { id: widgetRow.id } };
}

async function validateWidgetPosition(
  widget: CreateWidgetBody['widget'],
  widgetRow: CreateWidgetBody['widgetRow'],
  userId: number,
  companyId: number,
) {
  const isCreate = widgetRow.action === 'CREATE';
  const isConnect = widgetRow.action === 'CONNECT';

  if (isCreate && widget.position > widgetRow.size) {
    throw new ConflictError('Widget position cannot be greater than widget row size');
  }

  if (isConnect) {
    const existingWidgetRow = await repository.getWidgetRowByIdWithWidgetAtPosition(
      widgetRow.id,
      widget.position,
      companyId,
      userId,
    );

    if (!existingWidgetRow) {
      throw new ConflictError('Widget row not found');
    }

    if (existingWidgetRow.widgets.length !== 0) {
      throw new ConflictError('Widget at position already exists');
    }

    if (widget.position > existingWidgetRow.size) {
      throw new ConflictError('Widget position cannot be greater than widget row size');
    }
  }
}

function getWidgetRedisKey(companyId: number, widgetId: number) {
  return `widget:${companyId}${widgetId}`;
}

function saveWidgetAiSessionIdInRedis(companyId: number, widgetId: number, aiSessionId: string) {
  const oneDayInSeconds = 86400;

  return redisService.SETEX(getWidgetRedisKey(companyId, widgetId), oneDayInSeconds, aiSessionId);
}

export default {
  handleWidgetRowsOnWidgetCreate,
  validateWidgetPosition,
  getWidgetRedisKey,
};
