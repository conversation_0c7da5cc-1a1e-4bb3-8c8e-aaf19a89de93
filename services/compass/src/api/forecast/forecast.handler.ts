import {
  bullMq,
  commonAppUsageHelpers,
  commonForecast,
  compassAiService,
  ConflictError,
  dateHelpers,
  forecastConstants,
  InternalServerError,
  juneService,
  NotFoundError,
  NotImplementedError,
} from '@compass/common';
import type { YearMonth } from '@compass/common/types';
import { db, Enums } from '@compass/database';
import type { Request, Response } from 'express';
import _ from 'lodash';
import ndjson from 'ndjson';

import { appUsageHelpers } from '@/src/shared/app-usage';
import { asyncWrapper } from '@/src/shared/helpers';
import { paramsIdValidator } from '@/src/shared/validators';
import type { TypedBodyRequest } from '@/src/types';

import constants from './forecast.constants';
import helpers from './forecast.helpers';
import repository from './forecast.repository';
import type {
  CreateForecastBody,
  DeleteForecastDataBody,
  RunWorkflowBody,
  UpdateForecastBody,
  UpdateForecastDataBody,
} from './forecast.validators';

const { forecastJobType } = forecastConstants;
const { jobs, priorities } = bullMq.entitiesProcessorConstants;
const { forecastResponseType, duplicateForecastType } = constants;

async function getForecast(req: Request, res: Response) {
  const { companyId } = req.user!;

  const id = await paramsIdValidator.optional().parseAsync(req.params.id);

  const forecast = await repository.getForecastWithContext(companyId, id);
  if (!forecast && id) throw new NotFoundError('Scenario');
  if (!forecast) return res.json({ responseType: forecastResponseType.notInitialized, data: {} });
  if (forecast.isBase) throw new ConflictError('Base scenario cannot be viewed.');

  const periods = dateHelpers.generatePeriods(
    forecast.context.startPeriod as YearMonth,
    forecast.endPeriod as YearMonth,
  );

  const [forecastData, balancesData] = await Promise.all([
    repository.getForecastData(companyId, forecast.id, forecast.context.currency),
    repository.getBalancesData(
      companyId,
      forecast.context.startPeriod as YearMonth,
      forecast.endPeriod as YearMonth,
      forecast.context.currency,
    ),
  ]);

  if (!forecastData || !balancesData) throw new InternalServerError();

  const resolvedPeriods = forecastData.flatMap((account) => [
    ...Object.keys(account.actualData),
    ...Object.keys(account.forecastedData),
  ]) as YearMonth[];

  const firstPeriodWithData = _.min(resolvedPeriods);

  return res.json({
    responseType: forecastResponseType.data,
    data: {
      id: forecast.id,
      isBase: forecast.isBase,
      isTracked: forecast.isTracked,
      updatedAt: forecast.updatedAt,
      currency: forecast.context.currency,
      periods: firstPeriodWithData ? periods.filter((period) => period >= firstPeriodWithData!) : periods,
      accounts: forecastData,
      balancesData,
    },
  });
}

async function getForecasts(req: Request, res: Response) {
  const { companyId } = req.user!;

  const forecasts = await repository.getForecasts(companyId);

  res.json(forecasts);
}

async function getWorkflowNodes(req: Request, res: Response) {
  const forecastId = await paramsIdValidator.parseAsync(req.params.id);
  const { companyId } = req.user!;

  const forecast = await repository.getForecast(forecastId, companyId);
  if (!forecast) throw new NotFoundError('Scenario');
  if (forecast.isBase) throw new ConflictError('Base scenario cannot be used for workflow.');

  const nodes = await compassAiService.getForecastWorkflowNodes({ companyId, forecastId });

  res.json(nodes);
}

async function getWorkflowNode(req: Request, res: Response) {
  const [forecastId, nodeId] = await Promise.all([
    paramsIdValidator.parseAsync(req.params.id),
    paramsIdValidator.parseAsync(req.params.node_id),
  ]);
  const { companyId } = req.user!;

  const forecast = await repository.getForecast(forecastId, companyId);
  if (!forecast) throw new NotFoundError('Scenario');
  if (forecast.isBase) throw new ConflictError('Base scenario cannot be used for workflow.');

  const node = await compassAiService.getForecastWorkflowNode({ nodeId });

  res.json(node);
}

async function removeWorkflowNode(req: Request, res: Response) {
  const [forecastId, nodeId] = await Promise.all([
    paramsIdValidator.parseAsync(req.params.id),
    paramsIdValidator.parseAsync(req.params.node_id),
  ]);
  const { companyId } = req.user!;

  const forecast = await repository.getForecast(forecastId, companyId);
  if (!forecast) throw new NotFoundError('Scenario');
  if (forecast.isBase) throw new ConflictError('Base scenario cannot be used for workflow.');

  await compassAiService.removeForecastNode({ company_id: companyId, forecast_id: forecastId, node_id: nodeId });

  res.status(204).send();
}

async function createForecast(req: TypedBodyRequest<CreateForecastBody>, res: Response) {
  const { companyId } = req.user!;
  const { duplicateForecast, sourceForecastId, name } = req.body;

  const existingForecast = await repository.getForecastByName(companyId, name);
  if (existingForecast) throw new ConflictError('Scenario with this name already exists.');

  const forecastLimit = 10;
  const count = await repository.getUserCreatedForecastCount(companyId);
  if (count >= forecastLimit) {
    throw new ConflictError(`Scenario limit reached. You can create up to ${forecastLimit} scenarios.`);
  }

  const sourceForecast = await repository.getSourceForecast(sourceForecastId, companyId);
  if (!sourceForecast) throw new NotFoundError('Source scenario');

  const newForecast = await commonForecast.repository.duplicateForecast({
    companyId,
    sourceForecastId: sourceForecast.id,
    isFullCopy: duplicateForecast === duplicateForecastType.full,
    name,
    isTracked: false,
  });

  return res.status(201).json(newForecast);
}

async function runWorkflow(req: TypedBodyRequest<RunWorkflowBody>, res: Response) {
  res.setHeader('Content-Type', 'application/x-ndjson');
  res.setHeader('Transfer-Encoding', 'chunked');
  res.setHeader('Cache-Control', 'no-cache');
  res.setHeader('Connection', 'keep-alive');

  const forecastId = await paramsIdValidator.parseAsync(req.params.id);
  const { userInput, currentNodeId } = req.body;
  const { companyId } = req.user!;

  const forecast = await repository.getForecastWithCompany(forecastId, companyId);
  if (!forecast) throw new NotFoundError('Scenario');
  if (forecast.isBase) throw new ConflictError('Base scenario cannot be used for workflow.');

  const stream = await compassAiService.getForecastWorkflowStream({
    company: {
      id: forecast.company.id,
      country: forecast.company.country.name,
      industry: forecast.company.industry.name,
      defaultCurrency: forecast.company.currency,
      name: forecast.company.name,
    },
    forecast_id: forecast.id,
    user_input: userInput,
    current_node_id: currentNodeId,
  });
  const parsedStream = stream.pipe(ndjson.parse());

  stream.on('error', (error) => helpers.handleStreamError(error, forecastId, res));

  parsedStream.on('error', (error) => helpers.handleStreamError(error, forecastId, res));

  res.on('close', () => {
    stream.destroy();
    parsedStream.destroy();
  });

  try {
    for await (const parsedJson of parsedStream) {
      res.write(`${JSON.stringify(parsedJson)}\n`);
    }
  } catch (error) {
    helpers.handleStreamError(error, forecastId, res);
  } finally {
    stream.destroy();
    parsedStream.destroy();
    if (!res.writableEnded) res.end();
  }
}

async function updateForecast(req: TypedBodyRequest<UpdateForecastBody>, res: Response) {
  const forecastId = await paramsIdValidator.parseAsync(req.params.id);

  const { id: userId, companyId } = req.user!;

  const forecast = await repository.getForecast(forecastId, companyId);
  if (!forecast) throw new NotFoundError('Scenario');
  if (forecast.isBase) throw new ConflictError('Base scenario cannot be updated.');

  if (req.body.name) {
    const existingForecast = await repository.getForecastByName(companyId, req.body.name, forecastId);
    if (existingForecast) throw new ConflictError('Scenario with this name already exists.');
  }

  const updatedForecast = await repository.updateForecast(forecastId, req.body);

  juneService.track.update(userId, 'Scenario');

  return res.json(updatedForecast);
}

async function updateForecastData(req: TypedBodyRequest<UpdateForecastDataBody>, res: Response) {
  const forecastId = await paramsIdValidator.parseAsync(req.params.id);
  const { companyId } = req.user!;
  const changes = req.body;

  const forecast = await repository.getForecast(forecastId, companyId);
  if (!forecast) throw new NotFoundError('Scenario');
  if (forecast.isBase) throw new ConflictError('Base scenario cannot be updated.');

  const leafCategoryIds = [...new Set(changes.map((c) => c.leafCategoryId))];
  const privateMerchantIds = [...new Set(changes.map((c) => c.privateMerchantId).filter(Boolean))] as Array<number>;
  const privateMerchantNames = [...new Set(changes.map((c) => c.privateMerchantName))];

  await repository.validateForeignKeys(companyId, { leafCategoryIds, privateMerchantIds });

  const [accountForecastsByPrivateMerchantId, accountForecastsByPrivateMerchantName] = await Promise.all([
    repository.getAccountForecastsByPrivateMerchantIds(forecastId, privateMerchantIds),
    repository.getAccountForecastsByPrivateMerchantNames(forecastId, privateMerchantNames),
  ]);

  const missingById = new Map<number, string>();
  const missingByName = new Set<string>();

  for (const change of changes) {
    const { privateMerchantId, privateMerchantName } = change;
    if (privateMerchantId && !accountForecastsByPrivateMerchantId[privateMerchantId]) {
      missingById.set(privateMerchantId, privateMerchantName);
    } else if (!privateMerchantId && !accountForecastsByPrivateMerchantName[privateMerchantName]) {
      missingByName.add(privateMerchantName);
    }
  }

  await Promise.all([
    ...Array.from(missingById.entries()).map(async ([privateMerchantId, privateMerchantName]) => {
      const newAccountForecast = await repository.createAccountForecast(
        forecastId,
        privateMerchantId,
        privateMerchantName,
      );
      accountForecastsByPrivateMerchantId[privateMerchantId] = newAccountForecast;
    }),
    ...Array.from(missingByName).map(async (privateMerchantName) => {
      const newAccountForecast = await repository.createAccountForecast(forecastId, null, privateMerchantName);
      accountForecastsByPrivateMerchantName[privateMerchantName] = newAccountForecast;
    }),
  ]);

  await db.$transaction(
    changes.map((change) => {
      const { privateMerchantId, privateMerchantName } = change;
      let accountForecastId: number;
      if (privateMerchantId) {
        accountForecastId = accountForecastsByPrivateMerchantId[privateMerchantId].id;
      } else {
        accountForecastId = accountForecastsByPrivateMerchantName[privateMerchantName].id;
      }
      return repository.upsertAccountMonthlyForecast(accountForecastId, change);
    }),
  );

  res.status(204).send();
}

async function deleteForecastData(_req: TypedBodyRequest<DeleteForecastDataBody>, _res: Response) {
  throw new NotImplementedError('This endpoint is not implemented yet.');
}

async function deleteForecast(req: Request, res: Response) {
  const id = await paramsIdValidator.parseAsync(req.params.id);
  const { id: userId, companyId } = req.user!;

  const forecast = await repository.getForecast(id, companyId);
  if (!forecast) throw new NotFoundError('Scenario');
  if (forecast.isBase) throw new ConflictError('Base scenario cannot be deleted.');

  await repository.deleteForecast(id, companyId);

  juneService.track.delete(userId, 'Scenario');

  res.status(204).send();
}

async function rerunBaseForecast(req: Request, res: Response) {
  const { id: userId, companyId } = req.user!;

  const forecast = await repository.getBaseForecast(companyId);
  if (!forecast) throw new ConflictError('Base scenario not found.');

  await db.$transaction(async (tx) => {
    await commonAppUsageHelpers.setFeatureInProcessing(companyId, Enums.AppUsageFeature.FORECAST, tx);

    await repository.deleteForecast(forecast.id, companyId, tx);

    const [newForecast] = await Promise.all([
      repository.createForecast(
        {
          name: 'Base Scenario',
          isBase: true,
          companyId,
          contextId: forecast.contextId,
          startPeriod: forecast.startPeriod as YearMonth,
          endPeriod: forecast.endPeriod as YearMonth,
        },
        tx,
      ),
      repository.updateContext(forecast.contextId, companyId, Enums.ContextStatus.ACCOUNT_FORECAST, tx),
    ]);

    await bullMq.entitiesProcessorService.add(
      jobs.forecast,
      {
        jobType: jobs.forecast,
        companyId,
        data: {
          type: forecastJobType.rerunForecast,
          contextId: forecast.contextId,
          forecastId: newForecast.id,
        },
      },
      { priority: priorities.initialForecast },
    );

    juneService.track.start(userId, 'Forecast job (rerun)');
  });

  await commonForecast.helpers.setQueuedProgress(companyId);

  res.status(202).send();
}

export default {
  getForecast: asyncWrapper(getForecast),
  getForecasts: asyncWrapper(getForecasts),
  getWorkflowNodes: asyncWrapper(getWorkflowNodes),
  getWorkflowNode: asyncWrapper(getWorkflowNode),
  removeWorkflowNode: asyncWrapper(removeWorkflowNode),
  createForecast: asyncWrapper(createForecast),
  runWorkflow: asyncWrapper(runWorkflow),
  updateForecast: asyncWrapper(updateForecast),
  updateForecastData: asyncWrapper(updateForecastData),
  deleteForecastData: asyncWrapper(deleteForecastData),
  deleteForecast: asyncWrapper(deleteForecast),
  rerunBaseForecast: asyncWrapper(appUsageHelpers.errorWrapper(rerunBaseForecast)),
};
