# Forecast documentation

## Context

`Context` is a collection of information that is used by AI to create forecasts. It is created based on historical transactions, invoices and bills of the company. `AccountContext` is created for each of the accounts (`PrivateMerchants`) the company has, provided they have historical data. Context is created after the user connects their bank account, uploads a transaction template, or connects to an accounting system.

## Base Forecast

`Base Forecast` (`Meta Forecast`) is the initial forecast created immediately after the context creation has finished.

After the initial `Base Forecast` is created an exact copy of the forecast is made named `Smart Forecast`. This is the forecast that the users interacts with while the `Base Forecast` is just for internal use.

## Forecast Workflow

Workflow is designed to allow the user to make changes to a forecast either by answering existing questions which were created when the forecast was created or starting a new conversation with <PERSON>. All of this is done through the chat on the frontend. The user input made in the chat is sent to the backend and passed through to the CompassAI API. The CompassAI API processes the input and streams the response.

### Compass AI Endpoints

There are four endpoints used in the forecast workflow:

1. **POST /scenarios/nodes/initial**

Used to create the initial workflow nodes of the `smart forecast` on the AI side. The endpoint is called at the end of the `smart forecast` flow.

2. **GET /scenarios/nodes**

Used to retrieve the current workflow nodes of the forecast.

3. **POST /scenarios/workflow/stream**

Endpoint that receives the `user_input` along with other necessary data and streams the response back to the backend which passes it through to the frontend.

4. **POST /scenarios/remove-node**

Used to apply/discard the changes made in the forecast workflow. This endpoint is called when the user clicks the Apply or Discard button in the frontend. If apply was clicked the changes made to the forecast are saved in the Compass API database. If discard was clicked no changes are made to the forecast in the Compass API database. In both cases the backend calls the Compass AI's `POST /scenarios/remove-node` endpoint which removes the node on the AI side.

## Updating Forecasts

Frontend updates the forecasts in three different ways:

1. **Apply**

When the user clicks the Apply button, the frontend sends a request to the backend to apply the changes made in the forecast workflow. The backend calls the Compass AI's `POST /scenarios/remove-node` endpoint and updates the forecast in the Compass API database.

2. **Discard**

When the user clicks the Discard button, the frontend sends a request to the backend to discard the changes made in the forecast workflow. The backend then calls the `POST /scenarios/remove-node` and no changes to the forecast are made in the Compass API database.

3. **Manual Update**

When the user manually changes values in the forecast table and clicks save the changes made are updated in the Compass API database.

## cURL example of workflow request

This is a good way to see the streaming response from AI since Postman does not support streaming responses.

```bash
curl --location 'http://localhost:8000/v1/forecasts/1/workflow' \
--header 'Content-Type: application/json' \
--header 'X-Request-ID: e159a46a-a21e-4853-85ba-3351b8c37fc6' \
--header 'Cookie: auth-token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOjEsImlhdCI6MTc0MTE4NTQzNCwiZXhwIjoxNzcyNzIxNDM0fQ.yyt972Iv7NWDB8FLTciXbSX1GfVkoTxRmSgGvxDih0w' \
--data '{
    "user_input": "I want to spend 200k on 5 developers",
    "current_node_id": null
}'
```
