import {
  BadRequestError,
  bullMq,
  categorizationConstants,
  commonAppUsageHelpers,
  commonProgress,
  commonTransaction,
  ConflictError,
  juneService,
  mockDataConstants,
} from '@compass/common';
import { db, Enums } from '@compass/database';
import type { Request, Response } from 'express';

import { asyncWrapper } from '@/src/shared/helpers';
import type { TypedBodyRequest } from '@/src/types/express';

import helpers from './ai.helpers';
import repository from './ai.repository';
import type {
  CategorizeTransactionFilesBody,
  CategorizeTransactionsPlaidBody,
  CategorizeTransactionsSaltEdgeBody,
} from './ai.validators';

const { transactionCategorizationType } = categorizationConstants;
const { jobs, priorities } = bullMq.entitiesProcessorConstants;

async function categorizeTransactionsSaltEdge(
  req: TypedBodyRequest<CategorizeTransactionsSaltEdgeBody>,
  res: Response,
) {
  const { id: userId, companyId } = req.user!;
  const { connectionId } = req.body;

  const company = await repository.getCompanySaltEdge(companyId, connectionId);
  if (!company) throw new ConflictError('Company not found.', { connectionId });
  if (company.country.code === 'US') {
    throw new ConflictError('USA companies are not allowed to use SaltEdge.', { connectionId });
  }

  await db.$transaction(async (tx) => {
    await commonAppUsageHelpers.setFeatureInProcessing(companyId, Enums.AppUsageFeature.TRANSACTIONS, tx);

    await bullMq.entitiesProcessorService.add(
      jobs.transactionCategorization,
      {
        jobType: jobs.transactionCategorization,
        companyId,
        data: { type: transactionCategorizationType.saltEdgeConnection, connectionId },
      },
      { priority: priorities.initialCategorization },
    );
  });

  juneService.track.start(userId, 'SaltEdge connection job');

  await helpers.setQueuedProgress(companyId);

  res.status(202).send();
}

async function categorizeTransactionsPlaid(req: TypedBodyRequest<CategorizeTransactionsPlaidBody>, res: Response) {
  const { id: userId, companyId } = req.user!;
  const { plaidId } = req.body;

  const company = await repository.getCompanyPlaid(companyId, plaidId);
  if (!company) throw new ConflictError('Company not found.', { plaidId });
  if (company.country.code !== 'US') {
    throw new ConflictError('Only USA companies are allowed to use Plaid.', { plaidId });
  }

  await db.$transaction(async (tx) => {
    await commonAppUsageHelpers.setFeatureInProcessing(companyId, Enums.AppUsageFeature.TRANSACTIONS, tx);

    await bullMq.entitiesProcessorService.add(
      jobs.transactionCategorization,
      {
        jobType: jobs.transactionCategorization,
        companyId,
        data: { type: transactionCategorizationType.plaidConnection, plaidId },
      },
      { priority: priorities.initialCategorization },
    );
  });

  juneService.track.start(userId, 'Plaid connection job');

  await helpers.setQueuedProgress(companyId);

  res.status(202).send();
}

/**
 * Single file is already uploaded. That file is fetched and based on the data in the file
 * the BankAccount is created. That BankAccount is then assigned to transactions and the file.
 */
async function categorizeTransactionsOnboardingFile(req: Request, res: Response) {
  const { id: userId, companyId } = req.user!;

  const file = await repository.getSingleFile(companyId);
  if (!file) throw new ConflictError('File not found.');

  const company = await repository.getCompanyById(companyId);
  if (!company) throw new ConflictError('Company not found.');

  const transactions = await commonTransaction.getTransactionsService.getTransactionsFromFiles([file]);
  if (transactions.length === 0) throw new BadRequestError('No transactions found in the files.', { file });

  await commonAppUsageHelpers.checkEntitiesLength(companyId, Enums.AppUsageFeature.TRANSACTIONS, transactions.length);

  const currency = helpers.checkCurrency(transactions);

  const { ownerAccountName, ownerAccountNumber, ownerAccountType } = transactions[0];
  if (!ownerAccountName) throw new BadRequestError('Owner account name not found in the file.', { file });
  if (!ownerAccountNumber) throw new BadRequestError('Owner account number not found in the file.', { file });
  if (!ownerAccountType) throw new BadRequestError('Owner account type not found in the file.', { file });
  if (ownerAccountName === mockDataConstants.bankAccountName) {
    throw new ConflictError('Cannot upload transactions to demo bank account.', { file });
  }

  const { id: bankAccountId } = await repository.getOrCreateBankAccount({
    companyId,
    number: ownerAccountNumber,
    name: ownerAccountName,
    displayName: ownerAccountName,
    type: ownerAccountType,
    currency,
  });

  await db.$transaction(async (tx) => {
    await commonAppUsageHelpers.setFeatureInProcessing(companyId, Enums.AppUsageFeature.TRANSACTIONS, tx);

    await bullMq.entitiesProcessorService.add(
      jobs.transactionCategorization,
      {
        jobType: jobs.transactionCategorization,
        companyId,
        data: { type: transactionCategorizationType.onboardingFile, bankAccountId, file },
      },
      { priority: priorities.initialCategorization },
    );
  });

  juneService.track.start(userId, 'File job (transactions)', 'onboarding');

  await helpers.setQueuedProgress(companyId);

  res.status(202).send();
}

async function categorizeTransactionsNewFiles(req: TypedBodyRequest<CategorizeTransactionFilesBody>, res: Response) {
  const { id: userId, companyId } = req.user!;
  const { bankAccountId, fileIds } = req.body;

  const company = await repository.getCompanyByIdWithFiles(companyId, bankAccountId, fileIds);

  if (!company) throw new ConflictError('Company not found.');
  if (company.files.length !== fileIds.length) throw new ConflictError('One or more files not found.', { fileIds });
  if (company.bankAccounts.length === 0) throw new ConflictError('Bank account not found.', { bankAccountId });
  if (!company.isFirstCategorizationDone) throw new ConflictError('Available after connecting your real data.');

  const transactions = await commonTransaction.getTransactionsService.getTransactionsFromFiles(company.files);
  if (transactions.length === 0) throw new BadRequestError('No transactions found in the files.', { fileIds });

  await commonAppUsageHelpers.checkEntitiesLength(companyId, Enums.AppUsageFeature.TRANSACTIONS, transactions.length);

  const currency = helpers.checkCurrency(transactions);

  if (currency !== company.bankAccounts[0].currency) {
    throw new BadRequestError('Currency in file does not match bank account currency.');
  }

  await db.$transaction(async (tx) => {
    await commonAppUsageHelpers.setFeatureInProcessing(companyId, Enums.AppUsageFeature.TRANSACTIONS, tx);

    await bullMq.entitiesProcessorService.add(
      jobs.transactionCategorization,
      {
        jobType: jobs.transactionCategorization,
        companyId,
        data: { type: transactionCategorizationType.settingsNewFiles, bankAccountId, fileIds },
      },
      { priority: priorities.initialCategorization },
    );
  });

  juneService.track.start(userId, 'File job (transactions)', 'settings');

  await helpers.setQueuedProgress(companyId);

  res.status(202).send();
}

async function getCategorizeTransactionsProgress(req: Request, res: Response) {
  const { companyId } = req.user!;

  const progress = await commonProgress.transactionCategorization.get(companyId);

  res.json({ progress });
}

export default {
  categorizeTransactionsSaltEdge: asyncWrapper(categorizeTransactionsSaltEdge),
  categorizeTransactionsPlaid: asyncWrapper(categorizeTransactionsPlaid),
  categorizeTransactionsOnboardingFile: asyncWrapper(categorizeTransactionsOnboardingFile),
  categorizeTransactionsNewFiles: asyncWrapper(categorizeTransactionsNewFiles),
  getCategorizeTransactionsProgress: asyncWrapper(getCategorizeTransactionsProgress),
};
