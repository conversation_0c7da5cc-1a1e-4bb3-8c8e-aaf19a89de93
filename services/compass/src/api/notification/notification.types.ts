import { Enums } from '@compass/database';

import constants from './notification.constants';

type NotificationType = (typeof constants.notificationTypes)[keyof typeof constants.notificationTypes];

export type Notification = {
  id: number;
  title: string;
  companyId: number;
  isViewed: boolean;
  createdAt: Date;
  priority: Enums.Priority;
  type: NotificationType;
  itemId: number;
  referenceId: number | null;
};
