import { compassAiHelpers, compassAiRepository, logger, NotFoundError } from '@compass/common';
import type { CompassAi } from '@compass/common/types';
import { Enums } from '@compass/database';
import type { AiAssistantConversation } from '@prisma/client';
import type { Response } from 'express';

import { aiAssistantComponentBuilder, aiAssistantComponentConstants } from '@/src/shared/ai-assistant-component';
import { appUsageRepository } from '@/src/shared/app-usage';

import repository from './ai-assistant.repository';
import type { AiAssistantConversationStreamData } from './ai-assistant.types';

const { aiAssistantComponentType } = aiAssistantComponentConstants;

async function getAiAssistantConversationById(
  id: number | undefined,
  companyId: number,
): Promise<AiAssistantConversation | null> {
  if (!id) return null;

  const aiAssistantConversation = await repository.getAiAssistantConversationById(id, companyId);
  if (!aiAssistantConversation) throw new NotFoundError('AI assistant conversation', { id });

  return aiAssistantConversation;
}

async function createAiAssistantChatStreamInput(
  aiSessionId: string | null,
  companyId: number,
  prompt: string,
): Promise<CompassAi.AiAssistantChatStreamInput> {
  const [company, leafCategories, alertGroups] = await Promise.all([
    repository.getCompanyById(companyId),
    compassAiRepository.getCompaniesLeafCategoriesForAi(companyId),
    repository.getAlertGroups(),
  ]);

  return {
    sessionId: aiSessionId,
    prompt,
    companyId,
    hasTransactions: company.hasTransactions,
    hasAccrual: company.hasAccrual,
    companyName: company.name,
    country: company.country.name,
    industry: company.industry.name,
    currencies: Object.values(Enums.Currency),
    defaultCurrency: company.currency,
    leafCategories,
    bankAccounts: compassAiHelpers.transformBankAccountsForAi(company.bankAccounts),
    labels: company.labels.map(({ name }) => name),
    alertGroups: alertGroups.map(({ name }) => name),
  };
}

async function handleAiAssistantConversationStreamError(
  error: unknown,
  responseData: AiAssistantConversationStreamData,
  appUsageId: number,
  res: Response,
) {
  logger.error(error, 'AI assistant conversation stream error');

  await appUsageRepository
    .decrementAppUsageById(appUsageId)
    .catch((error) => logger.error({ error, appUsageId }, 'Failed to decrement app usage in AI assistant'));

  if (responseData.component?.componentType === aiAssistantComponentType.error) return;

  responseData.component = aiAssistantComponentBuilder.buildErrorComponent('Something went wrong.');
  res.write(JSON.stringify(responseData) + '\n');
}

export default {
  getAiAssistantConversationById,
  createAiAssistantChatStreamInput,
  handleAiAssistantConversationStreamError,
};
