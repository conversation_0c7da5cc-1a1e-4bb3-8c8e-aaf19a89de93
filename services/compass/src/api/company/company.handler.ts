import { ConflictError, logger, plaidService, saltEdgeService } from '@compass/common';
import { db, userDynamoDb } from '@compass/database';
import type { Request, Response } from 'express';

import { asyncWrapper } from '@/src/shared/helpers';
import type { TypedBodyRequest } from '@/src/types';

import helpers from './company.helpers';
import repository from './company.repository';
import type { CompanySettingsBody, UpdateDisplayFlagsBody } from './company.validators';

async function getConnections(req: Request, res: Response) {
  const { companyId } = req.user!;

  const thirdPartyConnections = await repository.getThirdPartyConnections(companyId);

  res.json(thirdPartyConnections);
}

async function updateCompanySettings(req: TypedBodyRequest<CompanySettingsBody>, res: Response) {
  const { companyId, id: userId } = req.user!;

  const company = await repository.getCompanyById(companyId);
  if (!company) throw new ConflictError('Company not found.', { companyId });
  if (!company.isFirstCategorizationDone && company.currency !== req.body.currency) {
    throw new ConflictError('Updating the currency is available after connecting your real data.', { companyId });
  }

  const updatedCompany = await db.$transaction(async (tx) => {
    const updatedCompany = await repository.updateCompany(companyId, req.body, tx);

    if (company.currency !== updatedCompany.currency) {
      await helpers.updateWidgetsCurrency(userId, companyId, company.currency, updatedCompany.currency, tx);
    }

    return updatedCompany;
  });

  res.json(updatedCompany);
}

async function updateDisplayFlags(req: TypedBodyRequest<UpdateDisplayFlagsBody>, res: Response) {
  const { companyId } = req.user!;

  const displayFlags = await repository.getDisplayFlagsByCompanyId(companyId);
  if (!displayFlags) throw new ConflictError('Display flags not found.', { companyId });

  const updatedDisplayFlags = await repository.updateDisplayFlagsById(displayFlags.id, req.body);

  res.json(updatedDisplayFlags);
}

async function deleteCompany(req: Request, res: Response) {
  const { companyId, email } = req.user!;

  const company = await repository.getCompanyForDeletionById(companyId);
  if (!company) throw new ConflictError('Company not found.');

  await db.$transaction(async (tx) => {
    await repository.deleteCompany(companyId, tx);
    await userDynamoDb.deleteUser(email);
  });

  if (company.saltEdgeCustomerId) {
    await saltEdgeService
      .deleteCustomer(company.saltEdgeCustomerId)
      .catch((error) => logger.error(error, 'Failed to delete salt edge customer when deleting company.'));
  }

  if (company.plaidItems.length > 0) {
    await Promise.all(
      company.plaidItems.map(({ accessToken }) =>
        plaidService
          .deleteItem(companyId, accessToken)
          .catch((error) => logger.error(error, 'Failed to delete plaid item when deleting company.')),
      ),
    );
  }

  await helpers
    .deleteSchedules(companyId, company.country.code)
    .catch((error) => logger.error(error, 'Failed to delete schedules when deleting company.'));

  res.status(204).send();
}

export default {
  getConnections: asyncWrapper(getConnections),
  updateCompanySettings: asyncWrapper(updateCompanySettings),
  updateDisplayFlags: asyncWrapper(updateDisplayFlags),
  deleteCompany: asyncWrapper(deleteCompany),
};
