import { ConflictError } from '@compass/common';
import { db } from '@compass/database';

import config from '@/src/config';
import { bankingHelpers } from '@/src/shared/banking';

import repository from './banking.repository';
import type { CompanyWithConnections } from './banking.types';

function checkMultipleConnectionsAllowed({
  allowMultipleConnections,
  saltEdgeConnections,
  plaidItems,
}: {
  allowMultipleConnections: boolean;
  saltEdgeConnections: Array<{ id: number }>;
  plaidItems: Array<{ plaidId: string }>;
}) {
  return (
    config.ENVIRONMENT === 'production' &&
    !allowMultipleConnections &&
    (saltEdgeConnections.length >= 1 || plaidItems.length >= 1)
  );
}

async function deleteSaltEdgeConnection(
  connectionId: string,
  company: CompanyWithConnections,
  shouldDeleteTransactions: boolean,
): Promise<void> {
  const saltEdgeConnection = company.saltEdgeConnections.find((c) => c.connectionId === connectionId);
  if (!saltEdgeConnection) throw new ConflictError('SaltEdge connection not found.');

  const { partnerConsentId, saltEdgeAccounts } = saltEdgeConnection;
  if (!partnerConsentId) throw new ConflictError('No SaltEdge Partner consent id.');

  const bankAccountIds = saltEdgeAccounts.map(({ bankAccountId }) => bankAccountId);
  const saltEdgeAccountIds = saltEdgeAccounts.map(({ id }) => id);

  await db.$transaction(async (tx) => {
    await repository.deleteSaltEdgeTransactions(shouldDeleteTransactions, saltEdgeAccountIds, tx);
    await bankingHelpers.deleteSaltEdgeConnection({ connectionId, partnerConsentId, bankAccountIds }, tx);
  });
}

async function deletePlaidItem(
  plaidId: string,
  company: CompanyWithConnections,
  shouldDeleteTransactions: boolean,
): Promise<void> {
  const plaidItem = company.plaidItems.find((item) => item.plaidId === plaidId);
  if (!plaidItem) throw new ConflictError('Plaid connection not found.');

  const bankAccountIds = plaidItem.plaidAccounts.map(({ bankAccountId }) => bankAccountId);
  const plaidAccountIds = plaidItem.plaidAccounts.map(({ id }) => id);

  await db.$transaction(async (tx) => {
    await repository.deletePlaidTransactions(shouldDeleteTransactions, plaidAccountIds, tx);
    await bankingHelpers.deletePlaidItem(
      { companyId: company.id, plaidId, accessToken: plaidItem.accessToken, bankAccountIds },
      tx,
    );
  });
}

function deleteConnection(
  connectionId: string,
  company: CompanyWithConnections,
  shouldDeleteTransactions: boolean,
): Promise<void> {
  if (company.country.code === 'US') return deletePlaidItem(connectionId, company, shouldDeleteTransactions);

  return deleteSaltEdgeConnection(connectionId, company, shouldDeleteTransactions);
}

export default { checkMultipleConnectionsAllowed, deleteConnection };
