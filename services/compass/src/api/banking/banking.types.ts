import { Enums } from '@compass/database';

export type CompanyWithConnections = {
  id: number;
  saltEdgeConnections: Array<{
    id: number;
    connectionId: string;
    partnerConsentId: string;
    saltEdgeAccounts: Array<{ id: number; bankAccountId: number }>;
  }>;
  plaidItems: Array<{
    id: number;
    plaidId: string;
    accessToken: string;
    plaidAccounts: Array<{ id: number; bankAccountId: number }>;
  }>;
  country: { code: string };
};

export type ConnectionsWithAccounts = {
  status: Enums.ConnectionStatus;
  connectionId: string;
  updatedAt: Date;
  institutionName: string;
  accounts: Array<{ id: number; _count: { transactions: number } }>;
};

export type User = { name: string; email: string; status: Enums.CompanyUserStatus };
