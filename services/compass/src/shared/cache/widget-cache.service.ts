import { logger } from '@compass/common';
import { Redis } from 'ioredis';

interface WidgetCacheEntry {
  data: any;
  cachedAt: string;
  companyId: number;
  widgetId: number;
}

interface CacheMetadata {
  cachedAt: string;
  isStale: boolean;
  ageInMinutes: number;
}

class WidgetCacheService {
  private redis: Redis;
  private defaultTTL = 24 * 60 * 60; // 1 day in seconds

  constructor(redis: Redis) {
    this.redis = redis;
  }

  private getCacheKey(companyId: number, widgetId: number): string {
    return `widget:${companyId}:${widgetId}`;
  }

  private getMetadataKey(companyId: number, widgetId: number): string {
    return `widget:meta:${companyId}:${widgetId}`;
  }

  async get(companyId: number, widgetId: number): Promise<WidgetCacheEntry | null> {
    try {
      const cacheKey = this.getCacheKey(companyId, widgetId);
      const cachedData = await this.redis.get(cacheKey);
      
      if (!cachedData) {
        return null;
      }

      return JSON.parse(cachedData);
    } catch (error) {
      logger.error({ error, companyId, widgetId }, 'Failed to get widget from cache');
      return null;
    }
  }

  async set(companyId: number, widgetId: number, data: any): Promise<void> {
    try {
      const cacheKey = this.getCacheKey(companyId, widgetId);
      const metadataKey = this.getMetadataKey(companyId, widgetId);
      const cachedAt = new Date().toISOString();

      const cacheEntry: WidgetCacheEntry = {
        data,
        cachedAt,
        companyId,
        widgetId,
      };

      // Set data with TTL
      await this.redis.setex(cacheKey, this.defaultTTL, JSON.stringify(cacheEntry));
      
      // Set metadata with same TTL
      await this.redis.setex(metadataKey, this.defaultTTL, cachedAt);
      
      logger.debug({ companyId, widgetId }, 'Widget data cached successfully');
    } catch (error) {
      logger.error({ error, companyId, widgetId }, 'Failed to cache widget data');
    }
  }

  async getMetadata(companyId: number, widgetId: number): Promise<CacheMetadata | null> {
    try {
      const metadataKey = this.getMetadataKey(companyId, widgetId);
      const cachedAt = await this.redis.get(metadataKey);
      
      if (!cachedAt) {
        return null;
      }

      const cacheTime = new Date(cachedAt);
      const now = new Date();
      const ageInMinutes = Math.floor((now.getTime() - cacheTime.getTime()) / (1000 * 60));
      const isStale = ageInMinutes > 60; // Consider stale after 1 hour

      return {
        cachedAt,
        isStale,
        ageInMinutes,
      };
    } catch (error) {
      logger.error({ error, companyId, widgetId }, 'Failed to get cache metadata');
      return null;
    }
  }

  async invalidate(companyId: number, widgetId: number): Promise<void> {
    try {
      const cacheKey = this.getCacheKey(companyId, widgetId);
      const metadataKey = this.getMetadataKey(companyId, widgetId);
      
      await Promise.all([
        this.redis.del(cacheKey),
        this.redis.del(metadataKey),
      ]);
      
      logger.debug({ companyId, widgetId }, 'Widget cache invalidated');
    } catch (error) {
      logger.error({ error, companyId, widgetId }, 'Failed to invalidate widget cache');
    }
  }

  async invalidateCompanyWidgets(companyId: number): Promise<void> {
    try {
      const pattern = `widget:${companyId}:*`;
      const metaPattern = `widget:meta:${companyId}:*`;
      
      const [keys, metaKeys] = await Promise.all([
        this.redis.keys(pattern),
        this.redis.keys(metaPattern),
      ]);

      if (keys.length > 0 || metaKeys.length > 0) {
        await this.redis.del(...keys, ...metaKeys);
        logger.debug({ companyId, keysDeleted: keys.length + metaKeys.length }, 'Company widget cache invalidated');
      }
    } catch (error) {
      logger.error({ error, companyId }, 'Failed to invalidate company widget cache');
    }
  }

  async refresh(companyId: number, widgetId: number): Promise<void> {
    await this.invalidate(companyId, widgetId);
  }
}

export default WidgetCacheService;
