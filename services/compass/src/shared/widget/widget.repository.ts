import type { Transaction } from '@compass/common/types';
import { db } from '@compass/database';
import { Prisma } from '@prisma/client';

function updateWidgetById(widgetId: number, data: Prisma.WidgetUpdateArgs['data'], tx?: Transaction) {
  const database = tx ?? db;
  return database.widget.update({ where: { id: widgetId }, data, include: { widgetRow: true } });
}

export default { updateWidgetById };
