import { mockDataConstants } from '@compass/common';
import type { Transaction } from '@compass/common/types';
import { db, Enums } from '@compass/database';
import { monitoringInitialDataHelpers } from '@compass/monitoring';
import { widgetInitialDataHelpers } from '@compass/widget';
import { Prisma } from '@prisma/client';

import type { InitialForecastCompany } from './types';

function getUserByCompanyId(companyId: number) {
  return db.user.findUnique({ where: { companyId }, select: { id: true, name: true, email: true } });
}

function getAppUsage(companyId: number, feature: Enums.AppUsageFeature, tx: Transaction) {
  return tx.appUsage.findUnique({ where: { companyId_feature: { companyId, feature } }, select: { id: true } });
}

function getAlertsByCompanyId(companyId: number) {
  return db.alert.findMany({ where: { companyId, isActive: true } });
}

function decrementAppUsageById(id: number, tx: Transaction) {
  return tx.appUsage.update({ where: { id }, data: { usage: { decrement: 1 } } });
}

function updateAlertById(alertId: number, data: Prisma.AlertUpdateArgs['data']) {
  return db.alert.update({ where: { id: alertId }, data });
}

function updateCompanyById(companyId: number, data: Prisma.CompanyUpdateInput) {
  return db.company.update({ where: { id: companyId }, data });
}

async function getInitialForecastCompany(
  companyId: number,
): Promise<[InitialForecastCompany, Enums.Currency, number | null] | [null, null, null]> {
  const company = await db.company.findUnique({
    where: { id: companyId },
    select: {
      id: true,
      name: true,
      currency: true,
      industry: { select: { name: true } },
      country: { select: { name: true } },
      context: { select: { id: true } },
      _count: { select: { transactions: true, invoices: true, bills: true } },
    },
  });

  if (!company) return [null, null, null];

  return [
    {
      id: company.id,
      companyName: company.name,
      country: company.country.name,
      industry: company.industry.name,
      hasEntries: company._count.transactions + company._count.invoices + company._count.bills > 0,
    },
    company.currency,
    company.context?.id ?? null,
  ];
}

function createContext(data: Prisma.ContextCreateArgs['data'], tx: Transaction) {
  return tx.context.create({ select: { id: true }, data });
}

function createBaseForecast(data: Prisma.ForecastCreateArgs['data'], tx: Transaction) {
  return tx.forecast.create({ select: { id: true }, data: { ...data, isBase: true } });
}

function handlePreFirstCategorization(companyId: number, userId: number, currency: Enums.Currency, isAccrual: boolean) {
  const getInitialAlerts = isAccrual
    ? monitoringInitialDataHelpers.getInitialAccrualAlerts
    : monitoringInitialDataHelpers.getInitialCashFlowAlerts;

  const getInitialWidgetRows = isAccrual
    ? widgetInitialDataHelpers.getInitialAccrualWidgetRows
    : widgetInitialDataHelpers.getInitialCashFlowWidgetRows;

  return db.$transaction([
    db.bankAccount.deleteMany({ where: { companyId, name: mockDataConstants.bankAccountName } }),
    db.privateMerchant.deleteMany({ where: { companyId } }),
    db.widgetRow.deleteMany({ where: { companyId, userId } }),
    db.widgetPreview.deleteMany({ where: { companyId } }),
    db.alert.deleteMany({ where: { companyId, userId } }),
    db.alertPreview.deleteMany({ where: { companyId } }),
    db.invoice.deleteMany({ where: { companyId } }),
    db.bill.deleteMany({ where: { companyId } }),
    db.label.deleteMany({ where: { companyId } }),
    db.promptSuggestion.deleteMany({ where: { companyId } }),
    db.context.deleteMany({ where: { companyId } }),
    db.aiAssistantConversation.deleteMany({ where: { companyId } }),
    db.appUsage.updateMany({ where: { companyId }, data: { usage: 0 } }),
    ...getInitialAlerts(currency).map((alert) =>
      db.alert.create({
        data: {
          title: alert.title,
          prompt: alert.prompt,
          meta: alert.meta,
          evaluationStartDate: alert.evaluationStartDate,
          evaluationEndDate: alert.evaluationEndDate,
          userId,
          companyId,
          alertGroupId: alert.alertGroupId,
          externalDelivery: [Enums.AlertExternalDelivery.EMAIL],
        },
      }),
    ),
    ...getInitialWidgetRows(currency).map((row, rowIndex) =>
      db.widgetRow.create({
        data: {
          userId,
          companyId,
          position: rowIndex + 1,
          size: row.size,
          widgets: {
            createMany: {
              data: row.widgets.map((widget, widgetIndex) => ({
                position: widgetIndex + 1,
                meta: widget.meta,
                prompt: widget.prompt,
              })),
            },
          },
        },
      }),
    ),
  ]);
}

export default {
  getUserByCompanyId,
  getAppUsage,
  getAlertsByCompanyId,
  decrementAppUsageById,
  updateAlertById,
  updateCompanyById,
  handlePreFirstCategorization,
  getInitialForecastCompany,
  createContext,
  createBaseForecast,
};
