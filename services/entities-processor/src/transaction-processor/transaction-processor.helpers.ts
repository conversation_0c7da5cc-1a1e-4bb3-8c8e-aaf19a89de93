import { commonAppUsageHelpers, commonProgress } from '@compass/common';
import { db, Enums } from '@compass/database';

function getCompanyData(company: {
  id: number;
  userId: number;
  name: string;
  currency: Enums.Currency;
  country: { name: string; code: string };
  industry: { name: string };
  isFirstCategorizationDone: boolean;
  appFeaturesInProcessing: Array<Enums.AppUsageFeature>;
}) {
  return {
    id: company.id,
    userId: company.userId,
    name: company.name,
    currency: company.currency,
    country: company.country.name,
    countryCode: company.country.code,
    industry: company.industry.name,
    isFirstCategorizationDone: company.isFirstCategorizationDone,
    appFeaturesInProcessing: company.appFeaturesInProcessing,
  };
}

function unsetFeatureInProcessingAndUpdateProgress(companyId: number) {
  return db.$transaction(async (tx) => {
    await commonAppUsageHelpers.unsetFeatureInProcessing(companyId, Enums.AppUsageFeature.TRANSACTIONS, tx);
    await commonProgress.transactionCategorization.set(companyId, '100', 'Finishing up.');
  });
}

export default { getCompanyData, unsetFeatureInProcessingAndUpdateProgress };
