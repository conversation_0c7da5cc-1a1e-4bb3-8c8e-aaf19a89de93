import { commonAppUsageHelpers } from '@compass/common';
import type { Plaid, SaltEdge, Transaction, TransactionWithBankAccount } from '@compass/common/types';
import { db, Enums } from '@compass/database';
import { Prisma } from '@prisma/client';
import _ from 'lodash';

import type { DailyBalance, TransactionWithConvertedAmount } from './transaction-processor.types';

async function getCompanyByIdForSaltEdgeFlow(
  companyId: number,
  connectionId: string,
): Promise<SaltEdge.CompanyByIdForSaltEdgeFlow | null> {
  const company = await db.company.findUnique({
    where: { id: companyId },
    select: {
      id: true,
      name: true,
      currency: true,
      user: { select: { id: true } },
      country: { select: { name: true, code: true } },
      industry: { select: { name: true } },
      isFirstCategorizationDone: true,
      appFeaturesInProcessing: true,
      saltEdgeConnections: {
        where: { connectionId },
        select: {
          connectionId: true,
          saltEdgeAccounts: { select: { id: true, saltEdgeId: true, bankAccountId: true, lastTransactionId: true } },
        },
        take: 1,
      },
      appUsages: {
        where: { feature: Enums.AppUsageFeature.TRANSACTIONS },
        select: { max: true, usage: true, maxOverride: true },
      },
    },
  });

  if (!company) return null;
  if (!company.user) return null;

  // connectionId has a unique constraint so it should always be just one in this query
  return {
    id: company.id,
    userId: company.user.id,
    name: company.name,
    currency: company.currency,
    country: { name: company.country.name, code: company.country.code },
    industry: { name: company.industry.name },
    isFirstCategorizationDone: company.isFirstCategorizationDone,
    appFeaturesInProcessing: company.appFeaturesInProcessing,
    saltEdgeAccounts: company.saltEdgeConnections[0].saltEdgeAccounts,
    remainingUsage: commonAppUsageHelpers.getRemainingUsage(company.appUsages[0]),
  };
}

async function getCompanyByIdForPlaidFlow(
  companyId: number,
  plaidId: string,
): Promise<Plaid.CompanyByIdForPlaidFlow | null> {
  const company = await db.company.findUnique({
    where: { id: companyId },
    select: {
      id: true,
      name: true,
      currency: true,
      user: { select: { id: true } },
      country: { select: { name: true, code: true } },
      industry: { select: { name: true } },
      isFirstCategorizationDone: true,
      appFeaturesInProcessing: true,
      plaidItems: {
        where: { plaidId },
        select: {
          accessToken: true,
          plaidId: true,
          nextCursor: true,
          plaidAccounts: { select: { id: true, plaidId: true, bankAccountId: true } },
        },
        take: 1,
      },
      appUsages: {
        where: { feature: Enums.AppUsageFeature.TRANSACTIONS },
        select: { max: true, usage: true, maxOverride: true },
      },
    },
  });

  if (!company) return null;
  if (!company.user) return null;

  return {
    id: company.id,
    userId: company.user.id,
    name: company.name,
    currency: company.currency,
    country: { name: company.country.name, code: company.country.code },
    industry: { name: company.industry.name },
    isFirstCategorizationDone: company.isFirstCategorizationDone,
    appFeaturesInProcessing: company.appFeaturesInProcessing,
    accessToken: company.plaidItems[0].accessToken,
    nextCursor: company.plaidItems[0].nextCursor,
    plaidAccounts: company.plaidItems[0].plaidAccounts.map(({ id, plaidId, bankAccountId }) => ({
      id,
      plaidId,
      bankAccountId,
    })),
    remainingUsage: commonAppUsageHelpers.getRemainingUsage(company.appUsages[0]),
  };
}

function updatePlaidItemCursor(plaidId: string, nextCursor: string, tx: Transaction) {
  return tx.plaidItem.update({ where: { plaidId }, data: { nextCursor } });
}

async function getCompanyById(companyId: number) {
  const company = await db.company.findUnique({
    where: { id: companyId },
    select: {
      id: true,
      name: true,
      currency: true,
      user: { select: { id: true } },
      country: { select: { name: true, code: true } },
      industry: { select: { name: true } },
      isFirstCategorizationDone: true,
      appFeaturesInProcessing: true,
      appUsages: {
        where: { feature: Enums.AppUsageFeature.TRANSACTIONS },
        select: { max: true, usage: true, maxOverride: true },
      },
    },
  });

  if (!company) return null;
  if (!company.user) return null;

  const { user, ...rest } = company;

  return { ...rest, userId: user.id };
}

async function getCompanyByIdWithFiles(companyId: number, fileIds: Array<number>) {
  const company = await db.company.findUnique({
    where: { id: companyId },
    select: {
      id: true,
      name: true,
      currency: true,
      user: { select: { id: true } },
      isFirstCategorizationDone: true,
      appFeaturesInProcessing: true,
      country: { select: { name: true, code: true } },
      industry: { select: { name: true } },
      files: {
        select: { id: true, key: true, name: true, statementFormat: true, version: true },
        where: { type: Enums.FileType.TRANSACTIONS, isProcessed: false, id: { in: fileIds } },
      },
      appUsages: {
        where: { feature: Enums.AppUsageFeature.TRANSACTIONS },
        select: { max: true, usage: true, maxOverride: true },
      },
    },
  });

  if (!company) return null;
  if (!company.user) return null;

  const { user, ...rest } = company;

  return { ...rest, userId: user.id };
}

async function checkTransactionExists(companyId: number, transaction: TransactionWithBankAccount) {
  const { hash, transactionId } = transaction;

  const filter = Prisma.sql`
    "companyId" = ${companyId}
    AND ("transactionId" = ${transactionId} OR "hash" = ${hash})
  `;

  const result: Array<{ id: number }> = await db.$queryRaw`
    SELECT "id"
    FROM "Transaction"
    WHERE ${filter}

    UNION

    SELECT "id"
    FROM "UnprocessedTransaction"
    WHERE ${filter}

    LIMIT 1
  `;

  return result.length > 0;
}

function updateFiles(ids: Array<number>, bankAccountId: number, tx?: Transaction) {
  const database = tx ?? db;
  return database.file.updateMany({
    data: { isProcessed: true, bankAccountId },
    where: { id: { in: ids } },
  });
}

function updateConnectionsUpdatedAt(connectionIds: Array<string>) {
  return db.saltEdgeConnection.updateMany({
    where: { connectionId: { in: connectionIds } },
    data: { updatedAt: new Date() },
  });
}

function updateItemUpdatedAt(plaidId: string) {
  return db.plaidItem.update({ where: { plaidId }, data: { updatedAt: new Date() } });
}

function deleteHistoricalBalances(companyId: number, tx: Transaction) {
  return tx.bankAccountDailyBalance.deleteMany({ where: { companyId } });
}

function createHistoricalBalances(historicalBalances: Record<string, ReadonlyArray<DailyBalance>>, tx: Transaction) {
  return tx.bankAccountDailyBalance.createMany({ data: _.flatMap(Object.values(historicalBalances)) });
}

function updateCurrentBalances(historicalBalances: Record<string, ReadonlyArray<DailyBalance>>, tx: Transaction) {
  return Promise.all(
    Object.keys(historicalBalances).map((accountId) => {
      const balances = historicalBalances[accountId];
      const { bankAccountId, balance, convertedBalance } = balances[balances.length - 1];
      return tx.bankAccount.update({
        where: { id: bankAccountId },
        data: { currentBalance: balance, currentConvertedBalance: convertedBalance },
      });
    }),
  );
}

function createUnprocessedTransactions(companyId: number, transactions: ReadonlyArray<TransactionWithConvertedAmount>) {
  return db.unprocessedTransaction.createMany({
    data: transactions.map((t) => ({ companyId, ..._.omit(t, ['ownerAccountType', 'ownerAccountAddress']) })),
  });
}

function updateSaltEdgeAccountLastTransactionId(id: number, lastTransactionId: string, tx: Transaction) {
  return tx.saltEdgeAccount.update({ where: { id }, data: { lastTransactionId } });
}

function deleteUnprocessedTransactionsByRelation(
  companyId: number,
  where: Prisma.UnprocessedTransactionWhereInput,
  tx: Transaction,
) {
  return tx.unprocessedTransaction.deleteMany({ where: { companyId, ...where } });
}

export default {
  getCompanyByIdForSaltEdgeFlow,
  getCompanyByIdForPlaidFlow,
  updatePlaidItemCursor,
  getCompanyById,
  checkTransactionExists,
  getCompanyByIdWithFiles,
  updateFiles,
  updateConnectionsUpdatedAt,
  updateItemUpdatedAt,
  deleteHistoricalBalances,
  createHistoricalBalances,
  updateCurrentBalances,
  createUnprocessedTransactions,
  updateSaltEdgeAccountLastTransactionId,
  deleteUnprocessedTransactionsByRelation,
};
