import { categorizationConstants } from '@compass/common';
import type { CategorizationFile } from '@compass/common/types';
import { Enums } from '@compass/database';

const { transactionCategorizationType } = categorizationConstants;

export type ProcessTransactionsCompany = {
  id: number;
  userId: number;
  name: string;
  currency: Enums.Currency;
  country: string;
  countryCode: string;
  industry: string;
  isFirstCategorizationDone: boolean;
  appFeaturesInProcessing: Array<Enums.AppUsageFeature>;
};

export type ProcessingDataType = (typeof transactionCategorizationType)[keyof typeof transactionCategorizationType];

type SaltEdgeConnectionProcessingData = {
  type: (typeof transactionCategorizationType)['saltEdgeConnection'];
  connectionId: string;
  accountIds: Array<number>;
};

type SaltEdgeDailyProcessingData = {
  type: (typeof transactionCategorizationType)['saltEdgeDaily'];
  accountIds: Array<number>;
};

type PlaidConnectionProcessingData = {
  type: (typeof transactionCategorizationType)['plaidConnection'];
  plaidId: string;
  accountIds: Array<number>;
  nextCursor: string;
  accessToken: string;
};

type PlaidDailyProcessingData = {
  type: (typeof transactionCategorizationType)['plaidDaily'];
  plaidId: string;
  accountIds: Array<number>;
  nextCursor: string;
};

type OnboardingFileProcessingData = {
  type: (typeof transactionCategorizationType)['onboardingFile'];
  companyId: number;
  bankAccountId: number;
  file: CategorizationFile;
};

type SettingsNewFilesProcessingData = {
  type: (typeof transactionCategorizationType)['settingsNewFiles'];
  bankAccountId: number;
  fileIds: Array<number>;
};

export type ProcessingData =
  | SaltEdgeConnectionProcessingData
  | SaltEdgeDailyProcessingData
  | PlaidConnectionProcessingData
  | PlaidDailyProcessingData
  | OnboardingFileProcessingData
  | SettingsNewFilesProcessingData;

export type ProcessTransactionsContext = {
  company: ProcessTransactionsCompany;
  processingData: ProcessingData;
  remainingUsage: number;
};
