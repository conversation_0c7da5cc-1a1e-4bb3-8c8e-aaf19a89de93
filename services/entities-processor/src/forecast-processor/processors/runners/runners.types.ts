import type { CompassAi } from '@compass/common/types';

import { Context, Forecast } from '@/src/types';

export type ContextualizedAccount = {
  privateMerchantId: number;
  context: Context;
  contextAiData: CompassAi.AccountContextOutput['data'];
};

export type ForecastedAccount = {
  privateMerchantId: number;
  privateMerchantName: string;
  forecast: Forecast;
  forecastAiData: CompassAi.AccountForecastOutput['data'];
};
