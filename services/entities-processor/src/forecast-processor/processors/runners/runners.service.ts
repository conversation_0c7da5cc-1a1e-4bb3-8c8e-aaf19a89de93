import { commonProgress, logger, YearMonth } from '@compass/common';
import { db, Enums } from '@compass/database';
import _ from 'lodash';

import config from '@/src/config';
import type { Company, Context, Forecast } from '@/src/types';

import ForecastFlowContext from '../processor.context';
import { accountContextService } from './account-context';
import { accountForecastService } from './account-forecast';
import repository from './runners.repository';
import { ContextualizedAccount, ForecastedAccount } from './runners.types';

const { ACCOUNT_CONTEXT, ACCOUNT_FORECAST, DONE } = Enums.ContextStatus;

const chunkSize = config.FORECAST_CHUNK_SIZE;

function updateProgress(increment: number, companyId: number, flowContext: ForecastFlowContext) {
  const processedCount = flowContext.incrementProcessedCount(increment);
  return commonProgress.forecasting.set(
    companyId,
    flowContext.getStatus(),
    `${processedCount} of ${flowContext.getTotalCount()}`,
  );
}

async function runAccountsContextualization(
  company: Company,
  context: Context,
  flowContext: ForecastFlowContext,
  _period?: YearMonth, // previously used when doing context update
) {
  const privateMerchants = await repository.getPrivateMerchants(
    company.id,
    flowContext.getLastProcessedId(),
    context.startPeriod,
    context.endPeriod,
  );
  const totalCount = privateMerchants.length;

  flowContext.setTotalCount(totalCount);
  await commonProgress.forecasting.set(company.id, flowContext.getStatus(), `1 of ${totalCount}`);

  const privateMerchantsChunked = _.chunk(privateMerchants, chunkSize);

  for (const privateMerchants of privateMerchantsChunked) {
    try {
      const accountContextsToCreate = (
        await Promise.all(
          privateMerchants.map(({ id: privateMerchantId }) =>
            accountContextService.contextualize(privateMerchantId, company, context),
          ),
        )
      ).filter(Boolean) as Array<ContextualizedAccount>;

      if (accountContextsToCreate.length === 0) continue;

      const maxPrivateMerchantId = privateMerchants[privateMerchants.length - 1].id;

      await db.$transaction([
        ...accountContextsToCreate.map(repository.createAccountContext),
        repository.updateContextLastProcessedId(context.id, maxPrivateMerchantId),
      ]);

      updateProgress(privateMerchants.length, company.id, flowContext);
    } catch (error) {
      logger.error({ companyId: company.id, error, privateMerchants }, 'Failed to create account contexts');
      throw error;
    }
  }

  logger.info({ companyId: company.id }, 'All account contexts created');
}

async function runAccountsForecast(
  company: Company,
  context: Context,
  forecast: Forecast,
  flowContext: ForecastFlowContext,
) {
  const privateMerchants = await repository.getPrivateMerchants(
    company.id,
    flowContext.getLastProcessedId(),
    context.startPeriod,
    context.endPeriod,
  );
  const totalCount = privateMerchants.length;

  const privateMerchantById = _.keyBy(privateMerchants, 'id');

  flowContext.setTotalCount(totalCount);
  await commonProgress.forecasting.set(company.id, flowContext.getStatus(), `1 of ${totalCount}`);

  const privateMerchantsChunked = _.chunk(privateMerchants, chunkSize);

  for (const privateMerchants of privateMerchantsChunked) {
    try {
      const accountForecastsToCreate = (
        await Promise.all(
          privateMerchants.map(({ id: privateMerchantId }) =>
            accountForecastService.forecast(privateMerchantId, company, context, forecast),
          ),
        )
      ).filter(Boolean) as Array<ForecastedAccount>;

      if (accountForecastsToCreate.length === 0) continue;

      const maxPrivateMerchantId = privateMerchants[privateMerchants.length - 1].id;

      await db.$transaction([
        ...accountForecastsToCreate.map((accountForecast) =>
          repository.createAccountForecast({
            ...accountForecast,
            privateMerchantName: privateMerchantById[accountForecast.privateMerchantId].displayName,
          }),
        ),
        repository.updateContextLastProcessedId(context.id, maxPrivateMerchantId),
      ]);

      updateProgress(privateMerchants.length, company.id, flowContext);
    } catch (error) {
      logger.error({ companyId: company.id, error, privateMerchants }, 'Failed to create account forecasts');
      throw error;
    }
  }

  logger.info({ companyId: company.id }, 'All account forecasts created');
}

async function runForecasting(
  company: Company,
  context: Context,
  forecast: Forecast,
  flowContext: ForecastFlowContext,
) {
  if (flowContext.getStatus() === ACCOUNT_CONTEXT) {
    await runAccountsContextualization(company, context, flowContext);

    const updatedContext = await repository.updateContextStatus(context.id, ACCOUNT_FORECAST);

    flowContext.updateStatus(updatedContext.status);
  }

  if (flowContext.getStatus() === ACCOUNT_FORECAST) {
    await runAccountsForecast(company, context, forecast, flowContext);

    const updatedContext = await repository.updateContextStatus(context.id, DONE);

    flowContext.updateStatus(updatedContext.status);
  }

  await commonProgress.forecasting.set(company.id, flowContext.getStatus(), 'Finishing up.');
}

async function rerunForecasting(
  company: Company,
  context: Context,
  forecast: Forecast,
  flowContext: ForecastFlowContext,
) {
  if (flowContext.getStatus() === ACCOUNT_FORECAST) {
    await runAccountsForecast(company, context, forecast, flowContext);

    const updatedContext = await repository.updateContextStatus(context.id, DONE);

    flowContext.updateStatus(updatedContext.status);
  }

  await commonProgress.forecasting.set(company.id, flowContext.getStatus(), 'Finishing up.');
}

async function runContextUpdate(
  company: Company,
  context: Context,
  flowContext: ForecastFlowContext,
  period: YearMonth,
) {
  if (flowContext.getStatus() === ACCOUNT_CONTEXT) {
    await runAccountsContextualization(company, context, flowContext, period);

    const updatedContext = await repository.updateContextStatus(context.id, DONE);

    flowContext.updateStatus(updatedContext.status);
  }

  await commonProgress.forecasting.set(company.id, flowContext.getStatus(), 'Finishing up.');
}

export default { runForecasting, rerunForecasting, runContextUpdate };
