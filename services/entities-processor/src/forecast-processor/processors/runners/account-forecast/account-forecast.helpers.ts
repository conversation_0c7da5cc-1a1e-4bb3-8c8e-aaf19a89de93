import type { CompassA<PERSON> } from '@compass/common/types';
import { Enums } from '@compass/database';

function createForecastPatternScope({
  leafCategoryId,
  scopeLeafCategoryIds,
  scopeType,
  scopeDescription,
}: {
  leafCategoryId: number | null;
  scopeLeafCategoryIds: Array<number>;
  scopeType: Enums.PatternScopeType;
  scopeDescription: string | null;
}): CompassAi.ForecastPatternScope {
  return {
    scopeType,
    leafCategoryIds: scopeLeafCategoryIds?.length > 0 ? scopeLeafCategoryIds : leafCategoryId ? [leafCategoryId] : null,
    description: scopeDescription || 'Pattern description',
  };
}

export default { createForecastPatternScope };
