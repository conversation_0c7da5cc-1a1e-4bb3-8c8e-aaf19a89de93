import { db } from '@compass/database';

import type { Company, Context } from '@/src/types';

function getContext(privateMerchantId: number, company: Company, context: Context) {
  return db.accountContext.findUnique({
    where: {
      privateMerchantId_contextId: { privateMerchantId, contextId: context.id },
      privateMerchant: { id: privateMerchantId, companyId: company.id },
    },
    select: {
      privateMerchant: { select: { id: true, displayName: true, type: true, info: true } },
      accountStatus: true,
      primaryDrivers: true,
      keyRisks: true,
      lastActivityDate: true,
      accountSummary: true,
      recurringPatterns: { omit: { id: true, contextId: true, createdAt: true, updatedAt: true } },
      seasonalities: { omit: { id: true, contextId: true, createdAt: true, updatedAt: true } },
      trends: { omit: { id: true, contextId: true, createdAt: true, updatedAt: true } },
    },
  });
}

export default {
  getContext,
};
