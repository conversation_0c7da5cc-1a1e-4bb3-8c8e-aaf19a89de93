import { compassAiService, dateHelpers, logger } from '@compass/common';
import type { CompassAi, YearMonth } from '@compass/common/types';
import { Enums } from '@compass/database';
import dayjs from 'dayjs';

import type { Company, Context, Forecast } from '@/src/types';

import type { ForecastedAccount } from '../runners.types';
import { dtoHelpers, forecastHelpers } from '../shared';
import helpers from './account-forecast.helpers';
import repository from './account-forecast.repository';

async function forecast(
  privateMerchantId: number,
  company: Company,
  context: Context,
  forecast: Forecast,
): Promise<ForecastedAccount | void> {
  const logData = { privateMerchantId, company: company.id, contextId: context.id, forecastId: forecast.id };

  const accountContext = await repository.getContext(privateMerchantId, company, context);
  if (!accountContext) return logger.warn(logData, 'No account context found.');

  logger.info(logData, 'Creating account forecast');

  const historicalData = await forecastHelpers.getAccountHistoricalData(
    company.id,
    privateMerchantId,
    context.currency,
    context.startPeriod,
    context.endPeriod,
  );

  const aiInput: CompassAi.AccountForecastInput = {
    company: dtoHelpers.getCompanyForAi(company, context.currency),
    account: dtoHelpers.getAccountForAi(accountContext.privateMerchant),
    historicalMonthlyData: historicalData,
    accountSummary: {
      accountStatus: accountContext.accountStatus || Enums.AccountStatus.UNKNOWN,
      primaryDrivers: accountContext.primaryDrivers || [],
      keyRisks: accountContext.keyRisks || [],
      lastActivityDate: accountContext.lastActivityDate || dayjs().format('YYYY-MM-DD'),
      summary: accountContext.accountSummary || 'Account summary not available',
    },
    startPeriod: forecast.startPeriod,
    endPeriod: forecast.endPeriod,
    recurringPatterns: accountContext.recurringPatterns.map((pattern) => ({
      scope: helpers.createForecastPatternScope(pattern),
      description: pattern.notes,
      frequency: pattern.frequency,
      typicalDayOfMonth: pattern.typicalDayOfMonth,
      dayVariance: pattern.dayVariance,
      typicalAmount: pattern.amount,
      amountVariance: pattern.amountVariance,
      firstObserved: pattern.firstObserved || context.startPeriod,
      lastObserved: pattern.lastObserved || context.endPeriod,
      occurrenceCount: pattern.occurrenceCount || 1,
    })),
    seasonalPatterns: accountContext.seasonalities.map((seasonality) => ({
      scope: helpers.createForecastPatternScope(seasonality),
      description: seasonality.notes,
      peakPeriods: dateHelpers.convertPeriodsToMonths(seasonality.peakPeriods),
      troughPeriods: dateHelpers.convertPeriodsToMonths(seasonality.troughPeriods),
      typicalAmount: seasonality.typicalAmount || 0,
      amountVariance: seasonality.amountVariance,
      peakMultiplier: seasonality.peakMultiplier,
      cyclesObserved: seasonality.cyclesObserved || 1,
    })),
    trends: accountContext.trends.map((trend) => ({
      scope: helpers.createForecastPatternScope(trend),
      description: trend.notes,
      trendType: trend.trendType,
      volatility: trend.volatility,
      trendStartPeriod: (trend.trendStartPeriod || context.startPeriod) as YearMonth,
      lastActivePeriod: (trend.lastActivePeriod || context.endPeriod) as YearMonth,
    })),
  };
  logger.info({ ...logData, aiInput }, 'Account forecast input data');

  const forecastAiData = await compassAiService.getAccountForecast(aiInput);

  logger.info({ ...logData, forecastAiData }, 'Account forecast output data');

  return {
    privateMerchantId,
    privateMerchantName: accountContext.privateMerchant.displayName,
    forecast,
    forecastAiData,
  };
}

export default { forecast };
