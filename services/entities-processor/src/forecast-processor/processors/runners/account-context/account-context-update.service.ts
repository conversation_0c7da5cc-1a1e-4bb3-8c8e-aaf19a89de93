import { /* compassAiService, */ logger } from '@compass/common';
import type { /* CompassAi, */ YearMonth } from '@compass/common/types';

import type { Company, Context } from '@/src/types';

import { /* dtoHelpers, */ forecastHelpers } from '../shared';
import repository, { type AccountContext } from './account-context.repository';
// import accountContextService from './account-context.service';
import type { LogData } from './account-context.types';

async function getAccountContext(
  privateMerchantId: number,
  company: Company,
  context: Context,
  logData: LogData,
): Promise<AccountContext> {
  const accountContext = await repository.getAccountContext(privateMerchantId, context.id);
  if (accountContext) return accountContext;

  logger.info(logData, 'Account context not found, creating new context');

  // const newAccountContext = await accountContextService.contextualize(privateMerchantId, company, context);
  // if (newAccountContext) return newAccountContext;

  logger.info(logData, 'No historical data for account, creating empty account context');

  return repository.createEmptyAccountContext(privateMerchantId, context);
}

async function contextualizeUpdate(
  privateMerchantId: number,
  company: Company,
  context: Context,
  updatePeriod: YearMonth,
): Promise<void> {
  const logData: LogData = { privateMerchantId, companyId: company.id, contextId: context.id };

  const merchant = await repository.getPrivateMerchant(privateMerchantId, company);
  if (!merchant) return logger.error(logData, 'Merchant not found');

  logger.info(logData, 'Creating account context update');

  const [historicalData] = await forecastHelpers.getAccountHistoricalData(
    company.id,
    privateMerchantId,
    context.currency,
    updatePeriod,
    updatePeriod,
  );
  if (historicalData === undefined) return logger.warn(logData, 'No historical data found for context update');

  const _accountContext = await getAccountContext(privateMerchantId, company, context, logData);

  // const aiInput: CompassAi.AccountContextUpdateInput = {
  //   company: dtoHelpers.getCompanyForAi(company, context.currency),
  //   account: dtoHelpers.getAccountForAi(merchant),
  //   newMonthData: historicalData,
  //   previousContext: {
  //     historicalPeriodContext: accountContext.monthlyContexts.map((monthlyContext) => ({
  //       id: monthlyContext.id,
  //       period: monthlyContext.period as YearMonth,
  //       summary: monthlyContext.summary,
  //       attentionLevel: monthlyContext.attentionLevel,
  //     })),
  //     historicalRecurringPatterns: accountContext.recurringPatterns.map((recurringPattern) => ({
  //       id: recurringPattern.id,
  //       leafCategoryId: recurringPattern.leafCategoryId,
  //       notes: recurringPattern.notes,
  //       amount: recurringPattern.amount,
  //       frequency: recurringPattern.frequency,
  //       typicalDayOfMonth: recurringPattern.typicalDayOfMonth,
  //     })),
  //     historicalSeasonalities: accountContext.seasonalities.map((seasonality) => ({
  //       id: seasonality.id,
  //       leafCategoryId: seasonality.leafCategoryId,
  //       notes: seasonality.notes,
  //       peakPeriods: seasonality.peakPeriods as Array<YearMonth>,
  //       troughPeriods: seasonality.troughPeriods as Array<YearMonth>,
  //     })),
  //     historicalTrends: accountContext.trends.map((trend) => ({
  //       id: trend.id,
  //       leafCategoryId: trend.leafCategoryId,
  //       notes: trend.notes,
  //       trendType: trend.trendType,
  //       growthRate: trend.growthRate,
  //       lastActivePeriod: trend.lastActivePeriod as YearMonth,
  //     })),
  //   },
  // };
  // logger.info({ ...logData, aiInput }, 'Account context update input data');

  // const contextAiData = await compassAiService.getAccountContextUpdate(aiInput);

  // logger.info({ ...logData, contextAiData }, 'Account context update output data');

  // await repository.createAccountContextUpdate(accountContext.id, historicalData, contextAiData);

  logger.info(logData, 'Created account context update');
}

export default { contextualizeUpdate };
