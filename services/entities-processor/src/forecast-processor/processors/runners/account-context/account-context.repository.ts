import { forecastConstants, logger } from '@compass/common';
import type { CompassAi } from '@compass/common/types';
import { db } from '@compass/database';
import { Prisma } from '@prisma/client';

import type { Company, Context } from '@/src/types';

import type { Amount, LeafCategoryId } from './account-context.types';

const accountContextSelect = Prisma.validator<Prisma.AccountContextSelect>()({
  id: true,
  reasoning: true,
  accountSummaryReasoning: true,
  accountStatus: true,
  primaryDrivers: true,
  keyRisks: true,
  lastActivityDate: true,
  accountSummary: true,
  recurringPatterns: { omit: { contextId: true, createdAt: true, updatedAt: true } },
  seasonalities: { omit: { contextId: true, createdAt: true, updatedAt: true } },
  trends: { omit: { contextId: true, createdAt: true, updatedAt: true } },
});
export type AccountContext = Prisma.AccountContextGetPayload<{ select: typeof accountContextSelect }>;

function getPrivateMerchant(privateMerchantId: number, company: Company) {
  return db.privateMerchant.findUnique({
    where: { id: privateMerchantId, companyId: company.id },
    select: { id: true, displayName: true, type: true, info: true },
  });
}

function getAccountContext(privateMerchantId: number, contextId: number): Promise<AccountContext | null> {
  return db.accountContext.findUnique({
    select: accountContextSelect,
    where: { privateMerchantId_contextId: { privateMerchantId, contextId } },
  });
}

function createEmptyAccountContext(privateMerchantId: number, context: Context): Promise<AccountContext> {
  return db.accountContext.create({
    data: { privateMerchantId, contextId: context.id },
    select: accountContextSelect,
  });
}

function createAccountContextUpdate(
  contextId: number,
  newMonthData: CompassAi.AccountContextUpdateInput['newMonthData'],
  contextAiData: CompassAi.AccountContextUpdateOutput['data'],
) {
  const amountsByLeafCategoryId: Record<LeafCategoryId, Amount> = {};
  for (const { leafCategoryId, amount } of newMonthData.leafCategoryAmounts) {
    amountsByLeafCategoryId[leafCategoryId] = amount;
  }

  // const accountMonthlyContextData: Prisma.AccountMonthlyContextCreateArgs['data'] = {
  //   contextId,
  //   totalAmount: newMonthData.totalAmount || 0,
  //   amountsByLeafCategoryId,
  //   period: contextAiData.changedPeriodContext.period,
  //   summary: contextAiData.changedPeriodContext.summary,
  //   attentionLevel: contextAiData.changedPeriodContext.attentionLevel,
  // };

  return db.$transaction(async (tx) => {
    return Promise.all([
      // tx.accountMonthlyContext.upsert({
      //   create: accountMonthlyContextData,
      //   update: accountMonthlyContextData,
      //   where: { period_contextId: { period: contextAiData.changedPeriodContext.period ?? '', contextId } },
      // }),
      ...contextAiData.changedRecurringPatterns.map((pattern) => {
        const where = { id: pattern.id ?? 0, contextId };

        if (pattern.changeInfo.action === forecastConstants.changeInfoAction.removed && pattern.id) {
          return tx.accountRecurringPattern
            .delete({ where })
            .catch((error) => logger.error({ error, pattern }, 'Failed to delete recurring pattern'));
        }

        const patternData: Prisma.AccountRecurringPatternCreateArgs['data'] = {
          contextId,
          leafCategoryId: pattern.leafCategoryId,
          notes: pattern.notes,
          amount: pattern.amount,
          frequency: pattern.frequency,
          typicalDayOfMonth: pattern.typicalDayOfMonth,
        };
        return tx.accountRecurringPattern.upsert({ create: patternData, update: patternData, where });
      }),
      ...contextAiData.changedSeasonalities.map((seasonality) => {
        const where = { id: seasonality.id ?? 0, contextId };

        if (seasonality.changeInfo.action === forecastConstants.changeInfoAction.removed && seasonality.id) {
          return tx.accountSeasonality
            .delete({ where })
            .catch((error) => logger.error({ error, seasonality }, 'Failed to delete seasonality'));
        }

        const accountSeasonalityData: Prisma.AccountSeasonalityCreateArgs['data'] = {
          contextId,
          leafCategoryId: seasonality.leafCategoryId,
          peakPeriods: seasonality.peakPeriods || [],
          troughPeriods: seasonality.troughPeriods || [],
          notes: seasonality.notes,
        };
        return tx.accountSeasonality.upsert({ create: accountSeasonalityData, update: accountSeasonalityData, where });
      }),
      ...contextAiData.changedTrends.map((trend) => {
        const where = { id: trend.id ?? 0, contextId };

        if (trend.changeInfo.action === forecastConstants.changeInfoAction.removed && trend.id) {
          return tx.accountTrend
            .delete({ where })
            .catch((error) => logger.error({ error, trend }, 'Failed to delete trend'));
        }

        const accountTrendData: Prisma.AccountTrendCreateArgs['data'] = {
          contextId,
          leafCategoryId: trend.leafCategoryId,
          notes: trend.notes,
          trendType: trend.trendType,
          growthRate: trend.growthRate,
          lastActivePeriod: trend.lastActivePeriod,
        };
        return tx.accountTrend.upsert({ create: accountTrendData, update: accountTrendData, where });
      }),
    ]);
  });
}

export default {
  getPrivateMerchant,
  getAccountContext,
  createEmptyAccountContext,
  createAccountContextUpdate,
};
