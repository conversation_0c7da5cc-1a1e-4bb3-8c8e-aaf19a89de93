import { compassAiService, logger } from '@compass/common';
import type { CompassAi } from '@compass/common/types';

import type { Company, Context } from '@/src/types';

import type { ContextualizedAccount } from '../runners.types';
import { dtoHelpers, forecastHelpers } from '../shared';
import repository from './account-context.repository';
import type { LogData } from './account-context.types';

async function contextualize(
  privateMerchantId: number,
  company: Company,
  context: Context,
): Promise<ContextualizedAccount | void> {
  const logData: LogData = { privateMerchantId, companyId: company.id, contextId: context.id };

  const merchant = await repository.getPrivateMerchant(privateMerchantId, company);
  if (!merchant) return logger.error(logData, 'Merchant not found');

  logger.info(logData, 'Creating account context');

  const historicalData = await forecastHelpers.getAccountHistoricalData(
    company.id,
    privateMerchantId,
    context.currency,
    context.startPeriod,
    context.endPeriod,
  );

  if (historicalData.length === 0) return logger.warn(logData, 'No historical data found');

  const aiInput: CompassAi.AccountContextInput = {
    company: dtoHelpers.getCompanyForAi(company, context.currency),
    account: dtoHelpers.getAccountForAi(merchant),
    historicalMonthlyData: historicalData,
  };
  logger.info({ ...logData, aiInput }, 'Account context input data');

  const contextAiData = await compassAiService.getAccountContext(aiInput);

  logger.info({ ...logData, contextAiData }, 'Account context output data');

  return { privateMerchantId, context, contextAiData };
}

export default { contextualize };
