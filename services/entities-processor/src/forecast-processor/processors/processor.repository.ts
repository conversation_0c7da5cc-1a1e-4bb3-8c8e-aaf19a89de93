import { ConflictError } from '@compass/common';
import type { BullMq, YearMonth } from '@compass/common/types';
import { db } from '@compass/database';

import type { GetContextUpdateCompanyResult, GetForecastCompanyResult } from './processor.types';

async function getForecastCompany(
  companyId: number,
  data: BullMq.EntitiesProcessor.ForecastJob | BullMq.EntitiesProcessor.RerunForecastJob,
): Promise<GetForecastCompanyResult> {
  const company = await db.company.findUnique({
    where: { id: companyId },
    select: {
      id: true,
      name: true,
      currency: true,
      industry: { select: { name: true } },
      country: { select: { name: true } },
      context: {
        where: { id: data.contextId },
        select: { id: true, currency: true, startPeriod: true, endPeriod: true, lastProcessedId: true, status: true },
      },
      forecasts: {
        where: { id: data.forecastId },
        select: { id: true, startPeriod: true, endPeriod: true },
      },
      user: { select: { id: true, name: true, email: true } },
    },
  });

  if (!company) throw new ConflictError('Company not found', data);
  if (!company.user) throw new ConflictError('User not found', data);
  if (!company.context) throw new ConflictError('Context not found', data);
  if (!company.forecasts[0]) throw new ConflictError('Forecast not found', data);

  return {
    company: {
      id: company.id,
      name: company.name,
      country: company.country.name,
      industry: company.industry.name,
    },
    context: {
      id: company.context.id,
      currency: company.context.currency,
      startPeriod: company.context.startPeriod as YearMonth,
      endPeriod: company.context.endPeriod as YearMonth,
    },
    contextStatus: {
      status: company.context.status,
      lastProcessedId: company.context.lastProcessedId,
    },
    forecast: {
      ...company.forecasts[0],
      startPeriod: company.forecasts[0].startPeriod as YearMonth,
      endPeriod: company.forecasts[0].endPeriod as YearMonth,
    },
    user: company.user,
  };
}

async function getContextUpdateCompany(
  companyId: number,
  data: BullMq.EntitiesProcessor.ContextUpdateJob,
): Promise<GetContextUpdateCompanyResult> {
  const company = await db.company.findUnique({
    where: { id: companyId },
    select: {
      id: true,
      name: true,
      currency: true,
      industry: { select: { name: true } },
      country: { select: { name: true } },
      context: {
        where: { id: data.contextId },
        select: { id: true, currency: true, startPeriod: true, endPeriod: true, lastProcessedId: true, status: true },
      },
    },
  });

  if (!company) throw new ConflictError('Company not found', data);
  if (!company.context) throw new ConflictError('Context not found', data);

  return {
    company: {
      id: company.id,
      name: company.name,
      country: company.country.name,
      industry: company.industry.name,
    },
    context: {
      id: company.context.id,
      currency: company.context.currency,
      startPeriod: company.context.startPeriod as YearMonth,
      endPeriod: company.context.endPeriod as YearMonth,
    },
    contextStatus: {
      status: company.context.status,
      lastProcessedId: company.context.lastProcessedId,
    },
  };
}

function setBaseForecastReadyDisplayFlag(companyId: number) {
  return db.companyDisplayFlag.update({
    where: { companyId },
    data: { showBaseForecastReady: true },
  });
}

export default { getForecastCompany, getContextUpdateCompany, setBaseForecastReadyDisplayFlag };
