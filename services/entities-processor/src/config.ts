import { awsValidators, configConstants } from '@compass/common';
import z from 'zod';

const configSchema = z.object({
  ENVIRONMENT: z.enum(configConstants.general.environments),

  DATABASE_URL: z.string(),

  WEB_APP_ORIGIN: z.string().url(),

  COMPASS_AI_URL: z.string().url(),

  REDIS_URL: z.string().url(),
  JOB_CONCURRENCY: z.coerce.number(),

  SALT_EDGE_APP_ID: z.string(),
  SALT_EDGE_SECRET: z.string(),

  PLAID_CLIENT_ID: z.string(),
  PLAID_SECRET: z.string(),
  PLAID_ENV: z.enum(['sandbox', 'production']),

  QUICKBOOKS_CLIENT_ID: z.string(),
  QUICKBOOKS_CLIENT_SECRET: z.string(),
  QUICKBOOK_API_BASE_URL: z.string().url(),

  AWS_ACCOUNT_ID: z.string(),
  AWS_REGION: z.string(),
  AWS_ACCESS_KEY_ID: awsValidators.credentialsValidator,
  AWS_SECRET_ACCESS_KEY: awsValidators.credentialsValidator,
  AWS_LOCAL_PREFIX: z.string().optional(),

  JUNE_WRITE_KEY: z.string(),

  FORECAST_CHUNK_SIZE: z.coerce.number(),
});

const config = {
  ENVIRONMENT: process.env.ENVIRONMENT,

  DATABASE_URL: process.env.DATABASE_URL,

  WEB_APP_ORIGIN: process.env.WEB_APP_ORIGIN,

  COMPASS_AI_URL: process.env.COMPASS_AI_URL,

  REDIS_URL: process.env.REDIS_URL,
  JOB_CONCURRENCY: process.env.JOB_CONCURRENCY,

  SALT_EDGE_APP_ID: process.env.SALT_EDGE_APP_ID,
  SALT_EDGE_SECRET: process.env.SALT_EDGE_SECRET,

  PLAID_CLIENT_ID: process.env.PLAID_CLIENT_ID,
  PLAID_SECRET: process.env.PLAID_SECRET,
  PLAID_ENV: process.env.PLAID_ENV,

  QUICKBOOKS_CLIENT_ID: process.env.QUICKBOOKS_CLIENT_ID,
  QUICKBOOKS_CLIENT_SECRET: process.env.QUICKBOOKS_CLIENT_SECRET,
  QUICKBOOK_API_BASE_URL: process.env.QUICKBOOK_API_BASE_URL,

  AWS_ACCOUNT_ID: process.env.AWS_ACCOUNT_ID,
  AWS_REGION: process.env.AWS_REGION,
  AWS_ACCESS_KEY_ID: process.env.AWS_ACCESS_KEY_ID,
  AWS_SECRET_ACCESS_KEY: process.env.AWS_SECRET_ACCESS_KEY,
  AWS_LOCAL_PREFIX: process.env.AWS_LOCAL_PREFIX,

  JUNE_WRITE_KEY: process.env.JUNE_WRITE_KEY,

  FORECAST_CHUNK_SIZE: process.env.FORECAST_CHUNK_SIZE,
};

export default configSchema.parse(config);
