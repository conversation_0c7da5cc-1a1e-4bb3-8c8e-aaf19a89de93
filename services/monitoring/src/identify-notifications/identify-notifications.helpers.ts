import { type DisplayQueriesDataItemsIds, type DisplayQueryData, monitoringConstants } from '@compass/monitoring';

function shouldCreateAlertNotification(
  displayQueriesData: Array<DisplayQueryData>,
  lastDisplayQueriesDataItemsIds: DisplayQueriesDataItemsIds,
) {
  if (displayQueriesData.every(({ items }) => items.length === 0)) return false;

  if (!lastDisplayQueriesDataItemsIds) return true;

  if (displayQueriesData.length === 1 && displayQueriesData[0].type === monitoringConstants.displayQueryType.custom) {
    return true;
  }

  const lastDisplayQueriesDataItemsIdsSets = lastDisplayQueriesDataItemsIds.map((ids) => new Set(ids));

  for (let i = 0; i < displayQueriesData.length; i++) {
    const displayQueryData = displayQueriesData[i];

    if (displayQueryData.type === monitoringConstants.displayQueryType.custom) continue;

    const lastDisplayQueriesDataItemsIdsSet = lastDisplayQueriesDataItemsIdsSets[i];

    for (const displayQueryDataItem of displayQueryData.items) {
      if (!lastDisplayQueriesDataItemsIdsSet.has(displayQueryDataItem.id)) return true;
    }
  }

  return false;
}

export default { shouldCreateAlertNotification };
