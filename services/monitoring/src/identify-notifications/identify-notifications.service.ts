import type { DisplayQueriesDataItemsIds } from '@compass/monitoring';

import type { TriggeredAlertWithData } from '../types';
import identifyNotificationsHelpers from './identify-notifications.helpers';

function identifyAlertsForNotification(
  triggeredAlertsWithData: Array<TriggeredAlertWithData>,
): Array<TriggeredAlertWithData> {
  return triggeredAlertsWithData.filter(({ alert, data }) => {
    const lastDisplayQueriesDataItemsIds = alert.lastDisplayQueriesDataItemsIds as DisplayQueriesDataItemsIds;
    return identifyNotificationsHelpers.shouldCreateAlertNotification(data, lastDisplayQueriesDataItemsIds);
  });
}

export default { identifyAlertsForNotification };
