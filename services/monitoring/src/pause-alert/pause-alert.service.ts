import { Prisma } from '@prisma/client';

import repository from '../repository';
import type { <PERSON><PERSON><PERSON><PERSON><PERSON>ser, IFailed<PERSON>lertsHandler, TriggeredAlertWithData } from '../types';

function excludePausedAlerts(triggeredAlertsWithData: Array<TriggeredAlertWithData>): Array<TriggeredAlertWithData> {
  return triggeredAlertsWithData.filter(({ alert }) => {
    const { isPauseEnabled, isPaused } = alert;
    return !isPauseEnabled || !isPaused;
  });
}
async function updateAlertPauseState(
  alert: AlertWithUser,
  isTriggered: boolean,
  isNotified: boolean,
  failedAlertsHandler: IFailedAlertsHandler,
): Promise<void> {
  const { id, isPauseEnabled, isPaused, currentTriggerCount, maxConsecutiveTriggers } = alert;

  if (!isPauseEnabled) return;

  try {
    const updates: Prisma.AlertUpdateArgs['data'] = {};

    if (isTriggered) {
      if (!isPaused && isNotified) {
        const newTriggerCount = currentTriggerCount + 1;
        updates.currentTriggerCount = newTriggerCount;

        if (newTriggerCount >= maxConsecutiveTriggers) {
          updates.isPaused = true;
        }
      }
    } else {
      if (currentTriggerCount > 0) {
        updates.isPaused = false;
        updates.currentTriggerCount = 0;
      }
    }

    if (Object.keys(updates).length > 0) {
      await repository.updateAlertById(id, updates);
    }
  } catch (error) {
    failedAlertsHandler.handleFailedAlerts([id], error, `Failed to update paused state for alert ${id}`);
  }
}

async function updateAlertPauseStates(
  allAlerts: Array<AlertWithUser>,
  triggeredAlertIds: Set<number>,
  notificationAlertIds: Set<number>,
  failedAlertsHandler: IFailedAlertsHandler,
): Promise<void> {
  await Promise.all(
    allAlerts.map(async (alert) => {
      const isTriggered = triggeredAlertIds.has(alert.id);
      const isNotified = notificationAlertIds.has(alert.id);
      return updateAlertPauseState(alert, isTriggered, isNotified, failedAlertsHandler);
    }),
  );
}

export default {
  excludePausedAlerts,
  updateAlertPauseStates,
};
