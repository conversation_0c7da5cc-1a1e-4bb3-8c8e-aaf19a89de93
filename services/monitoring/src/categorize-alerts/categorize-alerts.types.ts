import type { AlertWithUser, TriggeredAlertWithData } from '../types';
import categorizeAlertsConstants from './categorize-alerts.constants';

const { categories } = categorizeAlertsConstants;

export type CategorizedAlert =
  | { category: typeof categories.triggered; alertWithData: TriggeredAlertWithData }
  | { category: typeof categories.skipped; alert: AlertWithUser }
  | { category: typeof categories.nonTriggered; alert: AlertWithUser };

export type AlertCategorization = {
  triggeredAlerts: Array<TriggeredAlertWithData>;
  skippedAlerts: Array<AlertWithUser>;
  nonTriggeredAlerts: Array<AlertWithUser>;
};
