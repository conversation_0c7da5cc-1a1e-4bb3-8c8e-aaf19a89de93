import { type AlertMeta, monitoringHelpers, monitoringQueryService } from '@compass/monitoring';
import dayjs, { type Dayjs } from 'dayjs';

import type { AlertWithUser, IFailedAlertsHandler, TriggeredAlertWithData } from '../types';
import categorizeAlertsConstants from './categorize-alerts.constants';
import type { AlertCategorization, CategorizedAlert } from './categorize-alerts.types';

const { categories } = categorizeAlertsConstants;

async function categorizeAlert(
  alert: AlertWithUser,
  now: Dayjs,
  failedAlertsHandler: IFailedAlertsHandler,
): Promise<CategorizedAlert | undefined> {
  try {
    const { companyId, meta } = alert;
    const alertMeta = meta as AlertMeta;

    if (!monitoringHelpers.shouldCheckAlert(alert, now)) return { category: categories.skipped, alert };

    const numberOfAffectedRows = await monitoringQueryService.executeAlertQuery({
      sql: alertMeta.sqlQuery,
      companyId,
      validate: false,
    });

    if (numberOfAffectedRows === 0) return { category: categories.nonTriggered, alert };

    const data = await monitoringQueryService.executeAlertDisplayQueries({
      displayQueries: alertMeta.displayQueries,
      companyId,
    });

    return { category: categories.triggered, alertWithData: { alert, data } };
  } catch (error) {
    failedAlertsHandler.handleFailedAlerts([alert.id], error, `Failed to identify triggered alert ${alert.id}`);
  }
}

async function categorizeAlerts(
  alerts: Array<AlertWithUser>,
  failedAlertsHandler: IFailedAlertsHandler,
): Promise<AlertCategorization> {
  const now = dayjs();

  const categorizationResults = await Promise.all(
    alerts.map((alert) => categorizeAlert(alert, now, failedAlertsHandler)),
  );

  const triggeredAlerts: Array<TriggeredAlertWithData> = [];
  const skippedAlerts: Array<AlertWithUser> = [];
  const nonTriggeredAlerts: Array<AlertWithUser> = [];

  categorizationResults.forEach((result) => {
    if (result?.category === categories.triggered) {
      triggeredAlerts.push(result.alertWithData);
    } else if (result?.category === categories.skipped) {
      skippedAlerts.push(result.alert);
    } else if (result?.category === categories.nonTriggered) {
      nonTriggeredAlerts.push(result.alert);
    }
  });

  return { triggeredAlerts, skippedAlerts, nonTriggeredAlerts };
}

export default { categorizeAlerts };
